CREATE TABLE `property` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`owner_id` integer NOT NULL,
	`property_name` text NOT NULL,
	`plot_number` text NOT NULL,
	`street_name` text NOT NULL,
	`city` text NOT NULL,
	`state` text NOT NULL,
	`country` text NOT NULL,
	`bedrooms` integer NOT NULL,
	`bathrooms` integer NOT NULL,
	`base_rent` integer,
	`base_deposit` integer,
	`currency` text,
	`listing_date` text NOT NULL,
	`vacant` integer DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `property_alternative_id_unique` ON `property` (`alternative_id`);