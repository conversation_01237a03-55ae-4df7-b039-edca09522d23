{"id": "4df7335d-5eea-414b-9fc2-416b651c802c", "prevId": "646a9705-0580-4c0f-9f1e-2e8aa0e7f992", "version": "6", "dialect": "sqlite", "tables": {"account": {"name": "account", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "columnsFrom": ["user_id"], "tableTo": "user", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"session_token_unique": {"name": "session_token_unique", "columns": ["token"], "isUnique": true}}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "columnsFrom": ["user_id"], "tableTo": "user", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "verification": {"name": "verification", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "property": {"name": "property", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "alternative_id": {"name": "alternative_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "property_name": {"name": "property_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "plot_number": {"name": "plot_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "street_name": {"name": "street_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "bathrooms": {"name": "bathrooms", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "base_rent": {"name": "base_rent", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "base_deposit": {"name": "base_deposit", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "listing_date": {"name": "listing_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "vacant": {"name": "vacant", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {"property_alternative_id_unique": {"name": "property_alternative_id_unique", "columns": ["alternative_id"], "isUnique": true}}, "foreignKeys": {"property_owner_id_user_id_fk": {"name": "property_owner_id_user_id_fk", "tableFrom": "property", "columnsFrom": ["owner_id"], "tableTo": "user", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "lease": {"name": "lease", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "alternative_id": {"name": "alternative_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_date": {"name": "start_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "rent": {"name": "rent", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "deposit": {"name": "deposit", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "lease_status": {"name": "lease_status", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"lease_alternative_id_unique": {"name": "lease_alternative_id_unique", "columns": ["alternative_id"], "isUnique": true}}, "foreignKeys": {"lease_tenant_id_tenant_alternative_id_fk": {"name": "lease_tenant_id_tenant_alternative_id_fk", "tableFrom": "lease", "columnsFrom": ["tenant_id"], "tableTo": "tenant", "columnsTo": ["alternative_id"], "onUpdate": "no action", "onDelete": "no action"}, "lease_lease_status_lease_status_id_fk": {"name": "lease_lease_status_lease_status_id_fk", "tableFrom": "lease", "columnsFrom": ["lease_status"], "tableTo": "lease_status", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "lease_status": {"name": "lease_status", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"lease_status_status_unique": {"name": "lease_status_status_unique", "columns": ["status"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tenant": {"name": "tenant", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "alternative_id": {"name": "alternative_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "property_id": {"name": "property_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "date_of_birth": {"name": "date_of_birth", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "national_identity_number": {"name": "national_identity_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"tenant_alternative_id_unique": {"name": "tenant_alternative_id_unique", "columns": ["alternative_id"], "isUnique": true}, "tenant_national_identity_number_unique": {"name": "tenant_national_identity_number_unique", "columns": ["national_identity_number"], "isUnique": true}, "tenant_email_unique": {"name": "tenant_email_unique", "columns": ["email"], "isUnique": true}, "tenant_phone_unique": {"name": "tenant_phone_unique", "columns": ["phone"], "isUnique": true}}, "foreignKeys": {"tenant_property_id_property_alternative_id_fk": {"name": "tenant_property_id_property_alternative_id_fk", "tableFrom": "tenant", "columnsFrom": ["property_id"], "tableTo": "property", "columnsTo": ["alternative_id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "todo": {"name": "todo", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "completed": {"name": "completed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}, "internal": {"indexes": {}}}