PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_lease` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`property_id` text NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text NOT NULL,
	`rent` integer NOT NULL,
	`deposit` integer NOT NULL,
	`currency` text NOT NULL,
	`lease_status` integer NOT NULL,
	FOREIGN KEY (`property_id`) REFERENCES `property`(`alternative_id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`lease_status`) REFERENCES `lease_status`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_lease`("id", "alternative_id", "property_id", "start_date", "end_date", "rent", "deposit", "currency", "lease_status") SELECT "id", "alternative_id", "property_id", "start_date", "end_date", "rent", "deposit", "currency", "lease_status" FROM `lease`;--> statement-breakpoint
DROP TABLE `lease`;--> statement-breakpoint
ALTER TABLE `__new_lease` RENAME TO `lease`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `lease_alternative_id_unique` ON `lease` (`alternative_id`);--> statement-breakpoint
CREATE TABLE `__new_tenant` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`lease_id` text NOT NULL,
	`first_name` text NOT NULL,
	`last_name` text NOT NULL,
	`date_of_birth` text NOT NULL,
	`national_identity_number` text NOT NULL,
	`email` text NOT NULL,
	`phone` text NOT NULL,
	FOREIGN KEY (`lease_id`) REFERENCES `lease`(`alternative_id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_tenant`("id", "alternative_id", "lease_id", "first_name", "last_name", "date_of_birth", "national_identity_number", "email", "phone") SELECT "id", "alternative_id", "lease_id", "first_name", "last_name", "date_of_birth", "national_identity_number", "email", "phone" FROM `tenant`;--> statement-breakpoint
DROP TABLE `tenant`;--> statement-breakpoint
ALTER TABLE `__new_tenant` RENAME TO `tenant`;--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_alternative_id_unique` ON `tenant` (`alternative_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_national_identity_number_unique` ON `tenant` (`national_identity_number`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_email_unique` ON `tenant` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_phone_unique` ON `tenant` (`phone`);