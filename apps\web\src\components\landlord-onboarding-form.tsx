import { FormControl } from "@/components/ui/form"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, Building, FileText, Users, ChevronRight, ChevronLeft } from "lucide-react"
import {
  propertyInsertSchema,
  leaseInsertSchema,
  tenantInsertSchema,
  type PropertyInsert,
  type LeaseInsert,
  type TenantInsert,
} from "@/lib/schemas"

type OnboardingData = {
  properties: PropertyInsert[]
  leases: LeaseInsert[]
  tenants: TenantInsert[]
}

const steps = [
  { id: 1, title: "Properties", description: "Add your rental properties", icon: Building },
  { id: 2, title: "Leases", description: "Create lease agreements", icon: FileText },
  { id: 3, title: "Tenants", description: "Add tenant information", icon: Users },
]

const currencies = ["USD", "EUR", "GBP", "CAD", "AUD"]
const leaseStatuses = ["active", "pending", "expired", "terminated"]

export function LandlordOnboardingForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    properties: [],
    leases: [],
    tenants: [],
  })

  const propertyForm = useForm<PropertyInsert>({
    resolver: zodResolver(propertyInsertSchema),
    defaultValues: {
      property_name: "",
      plot_number: "",
      street_name: "",
      city: "",
      state: "",
      country: "",
      bedrooms: 0,
      bathrooms: 0,
      base_rent: 0,
      base_deposit: 0,
      currency: "USD",
      listing_date: new Date().toISOString().split("T")[0],
      vacant: true,
    },
  })

  const leaseForm = useForm<LeaseInsert>({
    resolver: zodResolver(leaseInsertSchema),
    defaultValues: {
      start_date: "",
      end_date: "",
      rent: 0,
      deposit: 0,
      currency: "USD",
      lease_status: "active",
    },
  })

  const tenantForm = useForm<TenantInsert>({
    resolver: zodResolver(tenantInsertSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      date_of_birth: "",
      national_identity_number: "",
      email: "",
      phone: "",
    },
  })

  const addProperty = (data: PropertyInsert) => {
    const propertyId = `prop_${Date.now()}`
    setOnboardingData((prev) => ({
      ...prev,
      properties: [...prev.properties, { ...data, alternative_id: propertyId }],
    }))
    propertyForm.reset()
  }

  const addLease = (data: LeaseInsert) => {
    const leaseId = `lease_${Date.now()}`
    setOnboardingData((prev) => ({
      ...prev,
      leases: [...prev.leases, { ...data, alternative_id: leaseId }],
    }))
    leaseForm.reset()
  }

  const addTenant = (data: TenantInsert) => {
    const tenantId = `tenant_${Date.now()}`
    setOnboardingData((prev) => ({
      ...prev,
      tenants: [...prev.tenants, { ...data, alternative_id: tenantId }],
    }))
    tenantForm.reset()
  }

  const removeProperty = (index: number) => {
    setOnboardingData((prev) => ({
      ...prev,
      properties: prev.properties.filter((_, i) => i !== index),
    }))
  }

  const removeLease = (index: number) => {
    setOnboardingData((prev) => ({
      ...prev,
      leases: prev.leases.filter((_, i) => i !== index),
    }))
  }

  const removeTenant = (index: number) => {
    setOnboardingData((prev) => ({
      ...prev,
      tenants: prev.tenants.filter((_, i) => i !== index),
    }))
  }

  const handleSubmit = () => {
    console.log("Onboarding data:", onboardingData)
    // Here you would typically send the data to your API
    alert("Onboarding completed! Check console for data.")
  }

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return onboardingData.properties.length > 0
      case 2:
        return onboardingData.leases.length > 0
      case 3:
        return onboardingData.tenants.length > 0
      default:
        return false
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const Icon = step.icon
          const isActive = currentStep === step.id
          const isCompleted = currentStep > step.id

          return (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex items-center space-x-2 ${isActive ? "text-primary" : isCompleted ? "text-green-600" : "text-muted-foreground"}`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                    isActive
                      ? "border-primary bg-primary text-primary-foreground"
                      : isCompleted
                        ? "border-green-600 bg-green-600 text-white"
                        : "border-muted-foreground"
                  }`}
                >
                  <Icon className="w-5 h-5" />
                </div>
                <div>
                  <p className="font-medium">{step.title}</p>
                  <p className="text-sm text-muted-foreground">{step.description}</p>
                </div>
              </div>
              {index < steps.length - 1 && <ChevronRight className="w-5 h-5 mx-4 text-muted-foreground" />}
            </div>
          )
        })}
      </div>

      {/* Step 1: Properties */}
      {currentStep === 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="w-5 h-5" />
              Add Properties
            </CardTitle>
            <CardDescription>
              Add all the properties you want to manage. You can add multiple properties.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Form {...propertyForm}>
              <form onSubmit={propertyForm.handleSubmit(addProperty)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={propertyForm.control}
                    name="property_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Property Name</FormLabel>
                        <Input placeholder="e.g., Sunset Apartments" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="plot_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Plot Number</FormLabel>
                        <Input placeholder="e.g., 123" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="street_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Name</FormLabel>
                        <Input placeholder="e.g., Main Street" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <Input placeholder="e.g., New York" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <Input placeholder="e.g., NY" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <Input placeholder="e.g., USA" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="bedrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bedrooms</FormLabel>
                        <Input
                          type="number"
                          value={field.value}
                          onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 0)}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="bathrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bathrooms</FormLabel>
                        <Input
                          type="number"
                          value={field.value}
                          onChange={(e) => field.onChange(Number.parseInt(e.target.value) || 0)}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="base_rent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Base Rent</FormLabel>
                        <Input
                          type="number"
                          value={field.value || ""}
                          onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="base_deposit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Base Deposit</FormLabel>
                        <Input
                          type="number"
                          value={field.value || ""}
                          onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency} value={currency}>
                                {currency}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={propertyForm.control}
                    name="listing_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Listing Date</FormLabel>
                        <Input type="date" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={propertyForm.control}
                  name="vacant"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      <div className="space-y-1 leading-none">
                        <FormLabel>Property is currently vacant</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Property
                </Button>
              </form>
            </Form>

            {/* Added Properties */}
            {onboardingData.properties.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Added Properties ({onboardingData.properties.length})</h3>
                <div className="grid gap-4">
                  {onboardingData.properties.map((property, index) => (
                    <Card key={index} className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{property.property_name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {property.plot_number} {property.street_name}, {property.city}, {property.state}
                          </p>
                          <div className="flex gap-2 mt-2">
                            <Badge variant="secondary">{property.bedrooms} bed</Badge>
                            <Badge variant="secondary">{property.bathrooms} bath</Badge>
                            {property.vacant && <Badge variant="outline">Vacant</Badge>}
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" onClick={() => removeProperty(index)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Step 2: Leases */}
      {currentStep === 2 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Add Leases
            </CardTitle>
            <CardDescription>Create lease agreements for your properties.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Form {...leaseForm}>
              <form onSubmit={leaseForm.handleSubmit(addLease)} className="space-y-4">
                <FormField
                  control={leaseForm.control}
                  name="property_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a property" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {onboardingData.properties.map((property) => (
                            <SelectItem key={property.alternative_id} value={property.alternative_id!}>
                              {property.property_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={leaseForm.control}
                    name="start_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={leaseForm.control}
                    name="end_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={leaseForm.control}
                    name="rent"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Monthly Rent</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            value={field.value}
                            onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={leaseForm.control}
                    name="deposit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Security Deposit</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            value={field.value}
                            onChange={(e) => field.onChange(Number.parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={leaseForm.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency} value={currency}>
                                {currency}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={leaseForm.control}
                    name="lease_status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lease Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {leaseStatuses.map((status) => (
                              <SelectItem key={status} value={status}>
                                {status.charAt(0).toUpperCase() + status.slice(1)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <Button type="submit" className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Lease
                </Button>
              </form>
            </Form>

            {/* Added Leases */}
            {onboardingData.leases.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Added Leases ({onboardingData.leases.length})</h3>
                <div className="grid gap-4">
                  {onboardingData.leases.map((lease, index) => {
                    const property = onboardingData.properties.find((p) => p.alternative_id === lease.property_id)
                    return (
                      <Card key={index} className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">{property?.property_name || "Unknown Property"}</h4>
                            <p className="text-sm text-muted-foreground">
                              {lease.start_date} to {lease.end_date}
                            </p>
                            <div className="flex gap-2 mt-2">
                              <Badge variant="secondary">
                                {lease.currency} {lease.rent}/month
                              </Badge>
                              <Badge variant="outline">{lease.lease_status}</Badge>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" onClick={() => removeLease(index)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </Card>
                    )
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Step 3: Tenants */}
      {currentStep === 3 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Add Tenants
            </CardTitle>
            <CardDescription>Add tenant information for your lease agreements.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Form {...tenantForm}>
              <form onSubmit={tenantForm.handleSubmit(addTenant)} className="space-y-4">
                <FormField
                  control={tenantForm.control}
                  name="lease_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lease Agreement</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a lease" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {onboardingData.leases.map((lease) => {
                            const property = onboardingData.properties.find(
                              (p) => p.alternative_id === lease.property_id,
                            )
                            return (
                              <SelectItem key={lease.alternative_id} value={lease.alternative_id!}>
                                {property?.property_name} ({lease.start_date} - {lease.end_date})
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={tenantForm.control}
                    name="first_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <Input placeholder="e.g., John" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={tenantForm.control}
                    name="last_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <Input placeholder="e.g., Doe" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={tenantForm.control}
                    name="date_of_birth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth</FormLabel>
                        <Input type="date" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={tenantForm.control}
                    name="national_identity_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>National Identity Number</FormLabel>
                        <Input placeholder="e.g., SSN or ID number" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={tenantForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <Input type="email" placeholder="e.g., <EMAIL>" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={tenantForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <Input placeholder="e.g., +****************" {...field} />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <Button type="submit" className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Tenant
                </Button>
              </form>
            </Form>

            {/* Added Tenants */}
            {onboardingData.tenants.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Added Tenants ({onboardingData.tenants.length})</h3>
                <div className="grid gap-4">
                  {onboardingData.tenants.map((tenant, index) => {
                    const lease = onboardingData.leases.find((l) => l.alternative_id === tenant.lease_id)
                    const property = onboardingData.properties.find((p) => p.alternative_id === lease?.property_id)
                    return (
                      <Card key={index} className="p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">
                              {tenant.first_name} {tenant.last_name}
                            </h4>
                            <p className="text-sm text-muted-foreground">{tenant.email}</p>
                            <p className="text-sm text-muted-foreground">{tenant.phone}</p>
                            {property && (
                              <Badge variant="outline" className="mt-2">
                                {property.property_name}
                              </Badge>
                            )}
                          </div>
                          <Button variant="ghost" size="sm" onClick={() => removeTenant(index)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </Card>
                    )
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setCurrentStep((prev) => Math.max(1, prev - 1))}
          disabled={currentStep === 1}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>

        {currentStep < 3 ? (
          <Button onClick={() => setCurrentStep((prev) => prev + 1)} disabled={!canProceed()}>
            Next
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        ) : (
          <Button onClick={handleSubmit} disabled={!canProceed()} className="bg-green-600 hover:bg-green-700">
            Complete Onboarding
          </Button>
        )}
      </div>
    </div>
  )
}
