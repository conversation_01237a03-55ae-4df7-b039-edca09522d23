import { Hono } from "hono";
import { db } from "@/db";
import { property } from "@/db/schema/property";
import { eq, desc } from "drizzle-orm";
import { z } from "zod";
import type { AuthType } from "@/lib/auth";
import { validate<PERSON>son } from "@/lib/validateJson";
import { lease, lease_status, leaseInsertSchema, leaseUpdateSchema } from "@/db/schema/lease";

const leaseRouter = new Hono<{ Bindings: CloudflareBindings, Variables: AuthType }>();

leaseRouter.use("*", async (c, next) => {

    const user = c.get("user");
    if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
    }

    const propertyId = c.req.param("propertyId");

    if (!propertyId) {
        return c.json({ error: "Property ID is required" }, 400);
    }

    const propertyRecord = await db
        .select({
            owner_id: property.owner_id,
        })
        .from(property)
        .where(eq(property.alternative_id, propertyId))
        .limit(1);

    if (propertyRecord.length === 0) {
        return c.json({ error: `Property not found`, related_key: propertyId }, 404);
    }

    // Check if user owns this property
    if (propertyRecord[0].owner_id !== user!.id) {
        return c.json({ error: "Forbidden" }, 403);
    }
    return next();
});

leaseRouter.get("/", async (c) => {
    try {
        const propertyId = c.req.param("propertyId");

        if (!propertyId) {
            return c.json({ error: "Property ID is required" }, 400);
        }

        const leases = await db
            .select({
                id: lease.alternative_id,
                start_date: lease.start_date,
                end_date: lease.end_date,
                rent: lease.rent,
                deposit: lease.deposit,
                currency: lease.currency,
                lease_status: lease_status.code,
            })
            .from(lease)
            .where(eq(lease.property_id, propertyId))
            .innerJoin(lease_status, eq(lease.lease_status, lease_status.id))
            .orderBy(desc(lease.start_date));

        return c.json({
            success: true,
            data: leases,
        });
    } catch (error) {
        console.error("Error fetching leases:", error);
        return c.json({ error: "Failed to fetch leases" }, 500);
    }
});

leaseRouter.get("/:leaseId", async (c) => {
    try {
        const leaseId = c.req.param("leaseId");

        if (!leaseId) {
            return c.json({ error: "Lease ID is required" }, 400);
        }
        const leaseRecord = await db
            .select({
                lease_id: lease.alternative_id,
                start_date: lease.start_date,
                end_date: lease.end_date,
                rent: lease.rent,
                deposit: lease.deposit,
                currency: lease.currency,
                lease_status: lease_status.code,
            })
            .from(lease)
            .where(eq(lease.alternative_id, leaseId))
            .innerJoin(lease_status, eq(lease.lease_status, lease_status.id))
            .limit(1);

        if (leaseRecord.length === 0) {
            return c.json({ error: `Lease not found`, related_key: leaseId }, 404);
        }

        return c.json({
            success: true,
            data: leaseRecord[0],
        });
    } catch (error) {
        console.error("Error fetching lease:", error);
        return c.json({ error: "Failed to fetch lease" }, 500);
    }
});

leaseRouter.post("/", async (c) => {
    try {
        const propertyId = c.req.param("propertyId");
        
        if (!propertyId) {
            return c.json({ error: "Property ID is required" }, 400);
        }
        
        const data = await validateJson(c, leaseInsertSchema) as z.infer<typeof leaseInsertSchema>;
                
        const leaseStatus = await db
        .select()
        .from(lease_status)
        .where(eq(lease_status.code, data.lease_status))
        .limit(1);
        
        if (leaseStatus.length === 0) {
            return c.json({ error: `A valid lease status code is required`, related_key: data.lease_status }, 400);
        }
        
        const alternative_id = crypto.randomUUID();
        // Generate UUID for alternative_id
        const newLease = await db.insert(lease).values({
            alternative_id,
            property_id: propertyId,
            start_date: data.start_date,
            end_date: data.end_date,
            rent: data.rent,
            deposit: data.deposit,
            currency: data.currency,
            lease_status: leaseStatus[0].id,
        }).returning({
            id: lease.alternative_id,
            start_date: lease.start_date,
            end_date: lease.end_date,
            rent: lease.rent,
            deposit: lease.deposit,
            currency: lease.currency,
            lease_status: lease_status.code,
        });

        return c.json({
            success: true,
            data: newLease[0],
        });
    }
    catch (error) {
        console.error("Error creating lease:", error);
        return c.json({ error: "Failed to create lease" }, 500);
    }
});

leaseRouter.put("/:leaseId", async (c) => {
    try {
        const propertyId = c.req.param("propertyId");
        const leaseId = c.req.param("leaseId");

        if (!propertyId) {
            return c.json({ error: "Property ID is required" }, 400);
        }

        if (!leaseId) {
            return c.json({ error: "Lease ID is required" }, 400);
        }

        const data = await validateJson(c, leaseUpdateSchema) as z.infer<typeof leaseUpdateSchema>;

        const leaseStatus = await db
            .select()
            .from(lease_status)
            .where(eq(lease_status.code, data.lease_status))
            .limit(1);

        if (data.lease_status && leaseStatus.length === 0) {
            return c.json({ error: `A valid lease status code is required`, related_key: data.lease_status }, 400);
        }

        const existingLease = await db
            .select()
            .from(lease)
            .where(eq(lease.alternative_id, leaseId))
            .limit(1);

        if (existingLease.length === 0) {
            return c.json({ error: `Lease not found`, related_key: leaseId }, 404);
        }

        if (existingLease[0].property_id !== propertyId) {
            return c.json({ error: "Forbidden" }, 403);
        }

        const leaseToUpdate = existingLease[0];

        //Check the lease fields and only update the ones which are changed
        if (data.start_date && data.start_date !== leaseToUpdate.start_date) {
            leaseToUpdate.start_date = data.start_date;
        }
        if (data.end_date && data.end_date !== leaseToUpdate.end_date) {
            leaseToUpdate.end_date = data.end_date;
        }
        if (data.rent && data.rent !== leaseToUpdate.rent) {
            leaseToUpdate.rent = data.rent;
        }
        if (data.deposit && data.deposit !== leaseToUpdate.deposit) {
            leaseToUpdate.deposit = data.deposit;
        }
        if (data.currency && data.currency !== leaseToUpdate.currency) {
            leaseToUpdate.currency = data.currency;
        }
        if (data.lease_status && leaseStatus[0].id !== leaseToUpdate.lease_status) {
            leaseToUpdate.lease_status = leaseStatus[0].id;
        }
        const updatedLease = await db
            .update(lease)
            .set(leaseToUpdate)
            .where(eq(lease.alternative_id, leaseId))
            .returning({
                id: lease.alternative_id,
                start_date: lease.start_date,
                end_date: lease.end_date,
                rent: lease.rent,
                deposit: lease.deposit,
                currency: lease.currency,
                lease_status: lease_status.code,
            });

        return c.json({
            success: true,
            data: updatedLease[0],
        });
    } catch (error) {
        console.error("Error updating lease:", error);
        return c.json({ error: "Failed to update lease" }, 500);
    }
});
export { leaseRouter };