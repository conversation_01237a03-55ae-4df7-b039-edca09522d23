{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "next lint", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@opennextjs/cloudflare": "^1.3.0", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.5", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.62.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^4.0.17"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.10", "typescript": "^5", "@tanstack/react-query-devtools": "^5.80.5", "wrangler": "^4.23.0"}}