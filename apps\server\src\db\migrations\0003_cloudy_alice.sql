CREATE TABLE `tenant` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`property_id` text NOT NULL,
	`first_name` text NOT NULL,
	`last_name` text NOT NULL,
	`date_of_birth` text NOT NULL,
	`national_identity_number` text NOT NULL,
	`email` text NOT NULL,
	`phone` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_alternative_id_unique` ON `tenant` (`alternative_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_national_identity_number_unique` ON `tenant` (`national_identity_number`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_email_unique` ON `tenant` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_phone_unique` ON `tenant` (`phone`);