import { z } from "zod"

// Property Schema
export const propertyInsertSchema = z.object({
  alternative_id: z.string().optional(),
  owner_id: z.string().optional(),
  property_name: z.string().min(1, "Property name is required"),
  plot_number: z.string().min(1, "Plot number is required"),
  street_name: z.string().min(1, "Street name is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  country: z.string().min(1, "Country is required"),
  bedrooms: z.number().min(0, "Bedrooms must be 0 or more"),
  bathrooms: z.number().min(0, "Bathrooms must be 0 or more"),
  base_rent: z.number().optional(),
  base_deposit: z.number().optional(),
  currency: z.string().optional(),
  listing_date: z.string().min(1, "Listing date is required"),
  vacant: z.boolean().default(true),
})

// Lease Schema
export const leaseInsertSchema = z.object({
  alternative_id: z.string().optional(),
  property_id: z.string().optional(),
  start_date: z.string().min(1, "Start date is required"),
  end_date: z.string().min(1, "End date is required"),
  rent: z.number().min(0, "Rent must be 0 or more"),
  deposit: z.number().min(0, "Deposit must be 0 or more"),
  currency: z.string().min(1, "Currency is required"),
  lease_status: z.string().min(1, "Lease status is required"),
})

// Tenant Schema
export const tenantInsertSchema = z.object({
  alternative_id: z.string().optional(),
  lease_id: z.string().optional(),
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  date_of_birth: z.string().min(1, "Date of birth is required"),
  national_identity_number: z.string().min(1, "National identity number is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().min(1, "Phone number is required"),
})

export type PropertyInsert = z.infer<typeof propertyInsertSchema>
export type LeaseInsert = z.infer<typeof leaseInsertSchema>
export type TenantInsert = z.infer<typeof tenantInsertSchema>
