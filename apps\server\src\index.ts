import { env } from "cloudflare:workers";
import { auth, type AuthType } from "./lib/auth";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { propertyRouter } from "./routers/property";
import { lookupRouter } from "./routers/lookup";
import { leaseRouter } from "./routers/lease";
import z from "zod";

const app = new Hono<{ Bindings: CloudflareBindings, Variables: AuthType }>(
  {
    strict: false
  }
);
app.use(logger());

app.onError((error, c) => {
  if (error instanceof z.ZodError) {
      return c.json({
        error: error.issues.map((e) => {
          return {
            field: e.path.join("."),
            message: e.message,
          };
        })
      }, 400);
    }
      if (error instanceof Error) {
      return c.json({ error: error.message }, 500);
    }
  return c.json({ error: "Internal Server Error" }, 500);
});

app.use(
  "/*",
  cors({
    origin: env.CORS_ORIGIN || "",
    allowMethods: ["GET", "POST", "OPTIONS", "PUT", "DELETE"],
    allowHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  })
);

app.use("*", async (c, next) => {
  const session = await auth.api.getSession({ headers: c.req.raw.headers });

  if (!session) {
    c.set("user", null);
    c.set("session", null);
    return next();
  }

  c.set("user", session.user);
  c.set("session", session.session);
  return next();
});

app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

app.get("/", (c) => {
  return c.text("OK");
});

app.get("/api/hello", (c) => {
  const user = c.get("user");
  return c.json({
    message: `Hello ${user?.name ?? "World"}!`,
  });
});

app.route("/api/lookup", lookupRouter);
app.route("/api/property", propertyRouter);
// app.route("/api/property/:propertyId/lease", leaseRouter);

export default app;
