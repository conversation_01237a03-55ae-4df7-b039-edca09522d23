(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a888_@tanstack_query-devtools_build_32241fb9._.js",
  "static/chunks/6a888_@tanstack_query-devtools_build_DevtoolsComponent_EDEL3XIZ_25047b39.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/6a888_@tanstack_query-devtools_build_fbbba049._.js",
  "static/chunks/6a888_@tanstack_query-devtools_build_DevtoolsPanelComponent_RN252AT2_25047b39.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-client] (ecmascript)");
    });
});
}}),
}]);