  @accessToken = {{loginRequest.response.body.token}}
  @propertyId = {{createProperty.response.body.data.alternative_id}}
  @propertyId = {{getAllProperties.response.body.data.0.id}}

###
# @name signupRequest
POST http://localhost:3000/api/auth/sign-up/email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "ThisIsADevPassWord201",
  "name": "Admin User"
}

###
# @name loginRequest
POST http://localhost:3000/api/auth/sign-in/email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "ThisIsADevPassWord201"
}

### 
# @name createProperty
POST http://localhost:3000/api/property
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "property_name": "Test Property 3",
  "plot_number": "3",
  "street_name": "Test Street",
  "city": "Test City",
  "state": "Test State",
  "country": "Test Country",
  "bedrooms": 3,
  "bathrooms": 1,
  "base_rent": 1,
  "base_deposit": 1,
  "currency": "USD",
  "listing_date": "2021-01-01",
  "vacant": true
}

###

### 
# @name getAllProperties
GET http://localhost:3000/api/property
Authorization: Bearer {{accessToken}}
Content-Type: application/json

###
# @name getPropertyById
GET http://localhost:3000/api/property/{{propertyId}}
Authorization: Bearer {{accessToken}}
Content-Type: application/json


###
# @name getPropertyTenantsById
GET http://localhost:3000/api/property/{{propertyId}}/tenants
Authorization: Bearer {{accessToken}}
Content-Type: application/json

###
# @name getPropertyLeasesById
GET http://localhost:3000/api/property/{{propertyId}}/leases
Authorization: Bearer {{accessToken}}
Content-Type: application/json


###
# @name updateProperty
PUT http://localhost:3000/api/property/{{propertyId}}
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "alternative_id": "{{propertyId}}",
  "property_name": "Test Property 4"
}
###
# @name deleteProperty
DELETE  http://localhost:3000/api/property/{{propertyId}}
Authorization: Bearer {{accessToken}}
Content-Type: application/json


###
# @name createLease
POST http://localhost:3000/api/property/{{propertyId}}/lease
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "start_date": "2021-01-01",
  "end_date": "2021-12-31",
  "rent": 1,
  "deposit": 1,
  "currency": "USD",
  "lease_status": "draft"
}

###
  @leaseId = {{getAllLeases.response.body.data.0.id}}

###
# @name getAllLeases
GET http://localhost:3000/api/property/{{propertyId}}/lease
Authorization: Bearer {{accessToken}}
Content-Type: application/json

###
# @name getLeaseById
GET http://localhost:3000/api/property/{{propertyId}}/lease/{{leaseId}}
Authorization: Bearer {{accessToken}}
Content-Type: application/json

###
# @name updateLease
PUT http://localhost:3000/api/property/{{propertyId}}/lease/{{leaseId}}
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "start_date": "2025-01-01",
  "end_date": "2025-12-31",
  "rent": 2000,
  "deposit": 2000.01,
  "currency": "USD",
  "lease_status": "draft"
}

###
# @name deleteLease
DELETE http://localhost:3000/api/property/{{propertyId}}/lease/{{leaseId}}
Authorization: Bearer {{accessToken}}
Content-Type: application/json


###
# @name createTenant
POST http://localhost:3000/api/property/{{propertyId}}/lease/{{leaseId}}/tenant
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "first_name": "Test",
  "last_name": "Tenant",
  "date_of_birth": "2021-01-01",
  "national_identity_number": "1234567890",
  "email": "<EMAIL>",
  "phone": "1234567890"
}

### 
# @name getAllTenants
GET http://localhost:3000/api/property/{{propertyId}}/tenants
Authorization: Bearer {{accessToken}}
Content-Type: application/json

