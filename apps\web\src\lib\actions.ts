"use server"

import { z } from "zod"
import { propertyInsertSchema, leaseInsertSchema, tenantInsertSchema } from "./schemas"

// Server action for property validation and saving
export async function createProperty(formData: FormData) {
  try {
    const data = {
      property_name: formData.get("property_name") as string,
      plot_number: formData.get("plot_number") as string,
      street_name: formData.get("street_name") as string,
      city: formData.get("city") as string,
      state: formData.get("state") as string,
      country: formData.get("country") as string,
      bedrooms: Number(formData.get("bedrooms")) || 0,
      bathrooms: Number(formData.get("bathrooms")) || 0,
      base_rent: Number(formData.get("base_rent")) || undefined,
      base_deposit: Number(formData.get("base_deposit")) || undefined,
      currency: formData.get("currency") as string,
      listing_date: formData.get("listing_date") as string,
      vacant: formData.get("vacant") === "on",
    }

    // Validate with Zod schema
    const validatedData = propertyInsertSchema.parse(data)

    // Here you would typically save to database
    console.log("Creating property:", validatedData)

    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors }
    }
    return { success: false, error: "Failed to create property" }
  }
}

// Server action for lease validation and saving
export async function createLease(formData: FormData) {
  try {
    const data = {
      property_id: formData.get("property_id") as string,
      start_date: formData.get("start_date") as string,
      end_date: formData.get("end_date") as string,
      rent: Number(formData.get("rent")) || 0,
      deposit: Number(formData.get("deposit")) || 0,
      currency: formData.get("currency") as string,
      lease_status: formData.get("lease_status") as string,
    }

    // Validate with Zod schema
    const validatedData = leaseInsertSchema.parse(data)

    // Here you would typically save to database
    console.log("Creating lease:", validatedData)

    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors }
    }
    return { success: false, error: "Failed to create lease" }
  }
}

// Server action for tenant validation and saving
export async function createTenant(formData: FormData) {
  try {
    const data = {
      lease_id: formData.get("lease_id") as string,
      first_name: formData.get("first_name") as string,
      last_name: formData.get("last_name") as string,
      date_of_birth: formData.get("date_of_birth") as string,
      national_identity_number: formData.get("national_identity_number") as string,
      email: formData.get("email") as string,
      phone: formData.get("phone") as string,
    }

    // Validate with Zod schema
    const validatedData = tenantInsertSchema.parse(data)

    // Here you would typically save to database
    console.log("Creating tenant:", validatedData)

    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error.flatten().fieldErrors }
    }
    return { success: false, error: "Failed to create tenant" }
  }
}
