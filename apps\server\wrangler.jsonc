{
  "name": "realtor-biz-africa-server",
  "main": "src/index.ts",
  "compatibility_date": "2025-06-15",
  "compatibility_flags": ["nodejs_compat"],
  "vars": {
    "NODE_ENV": "production"
    // Add public environment variables here
    // Example: "CORS_ORIGIN": "https://your-domain.com"
  }
  // For sensitive data, use:
  // wrangler secret put SECRET_NAME
  // Don't add secrets to "vars" - they're visible in the dashboard!

  ,
  // To set up D1 database:
  // 1. Run: wrangler login
  // 2. Run: wrangler d1 create your-database-name
  // 3. Copy the output and paste below
  // Then run migrations:
  // bun db:generate
  // To apply migrations locally, run:
  // wrangler d1 migrations apply YOUR_DB_NAME --local
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "product-listings-app-db",
      "database_id": "74bbc0e4-75fa-444c-a3e1-8c7dc3ac3418",
      "preview_database_id": "local-test-db",
      "migrations_dir": "./src/db/migrations"
    }
  ]
}
