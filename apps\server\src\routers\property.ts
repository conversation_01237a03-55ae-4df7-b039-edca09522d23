import { Hono } from "hono";
import { db } from "@/db";
import { property } from "@/db/schema/property";
import { eq, desc, inArray } from "drizzle-orm";
import { z } from "zod";
import type { AuthType } from "../lib/auth";
import { validate<PERSON><PERSON>, validateOutput<PERSON><PERSON> } from "../lib/validate<PERSON>son";
import { tenant } from "@/db/schema/tenant";
import { lease, lease_status } from "@/db/schema/lease";

const propertyRouter = new Hono<{ Bindings: CloudflareBindings, Variables: AuthType }>();

// Validation schemas
const createPropertySchema = z.object({
  property_name: z.string().min(1),
  plot_number: z.string().min(1),
  street_name: z.string().min(1),
  city: z.string().min(1),
  state: z.string().min(1),
  country: z.string().min(1),
  bedrooms: z.number().int().positive(),
  bathrooms: z.number().int().positive(),
  base_rent: z.number().optional(),
  base_deposit: z.number().optional(),
  currency: z.string().optional(),
  listing_date: z.string(),
  vacant: z.boolean().default(false),
});

const propertyOutputSchema = z.object({
  id: z.string(),
  property_name: z.string(),
  plot_number: z.string(),
  street_name: z.string(),
  city: z.string(),
  state: z.string(),
  country: z.string(),
  bedrooms: z.number(),
  bathrooms: z.number(),
  base_rent: z.number().optional(),
  base_deposit: z.number().optional(),
  currency: z.string().optional(),
  listing_date: z.string(),
  vacant: z.boolean(),
});

// Middleware to check authentication
propertyRouter.use("*", async (c, next) => {
  const user = c.get("user");
  if (!user) {
    return c.json({ error: "Unauthorized" }, 401);
  }
  return next();
});

// Create property
propertyRouter.post("/", async (c) => {
  try {
    const user = c.get("user");
    const data = await validateJson(c, createPropertySchema) as z.infer<typeof createPropertySchema>;

    // Generate UUID for alternative_id
    const alternative_id = crypto.randomUUID();

    const newProperty = await db.insert(property).values({
      alternative_id,
      owner_id: user!.id,
      property_name: data.property_name,
      plot_number: data.plot_number,
      street_name: data.street_name,
      city: data.city,
      state: data.state,
      country: data.country,
      bedrooms: data.bedrooms,
      bathrooms: data.bathrooms,
      base_rent: data.base_rent,
      base_deposit: data.base_deposit,
      currency: data.currency,
      listing_date: data.listing_date,
      vacant: data.vacant,
    }).returning();

    validateOutputJson(c, newProperty, propertyOutputSchema.array());

    return c.json({
      success: true,
      data: newProperty[0],
    });
  } catch (error) {
    console.error("Error creating property:", error);
    return c.json({ error: error instanceof Error ? error.message : "Failed to create property" }, 500);
  }
});

// Get all properties for the authenticated user
propertyRouter.get("/", async (c) => {
  try {
    const user = c.get("user");

    const properties = await db
      .select({
        id: property.alternative_id,
        property_name: property.property_name,
        plot_number: property.plot_number,
        street_name: property.street_name,
        city: property.city,
        state: property.state,
        country: property.country,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        base_rent: property.base_rent,
        base_deposit: property.base_deposit,
        currency: property.currency,
        listing_date: property.listing_date,
        vacant: property.vacant,
      })
      .from(property)
      .where(eq(property.owner_id, user!.id));

    validateOutputJson(c, properties, propertyOutputSchema.array());

    return c.json({
      success: true,
      data: properties,
    });
  } catch (error) {
    console.error("Error fetching properties:", error);
    return c.json({ error: "Failed to fetch properties" }, 500);
  }
});

// Get property by alternative_id
propertyRouter.get("/:id", async (c) => {
  try {
    const user = c.get("user");
    const id = c.req.param("id");

    const propertyRecord = await db
      .select()
      .from(property)
      .where(eq(property.alternative_id, id))
      .limit(1);

    if (propertyRecord.length === 0) {
      return c.json({ error: `Property not found`, related_key: id }, 404);
    }

    // Check if user owns this property
    if (propertyRecord[0].owner_id !== user!.id) {
      return c.json({ error: "Forbidden" }, 403);
    }
    const propertyResult: any = {
      id: propertyRecord[0].alternative_id,
      property_name: propertyRecord[0].property_name,
      plot_number: propertyRecord[0].plot_number,
      street_name: propertyRecord[0].street_name,
      city: propertyRecord[0].city,
      state: propertyRecord[0].state,
      country: propertyRecord[0].country,
      bedrooms: propertyRecord[0].bedrooms,
      bathrooms: propertyRecord[0].bathrooms,
      base_rent: propertyRecord[0].base_rent,
      base_deposit: propertyRecord[0].base_deposit,
      currency: propertyRecord[0].currency,
      listing_date: propertyRecord[0].listing_date,
      vacant: propertyRecord[0].vacant,
    };
    validateOutputJson(c, propertyResult, propertyOutputSchema.array());

    return c.json({
      success: true,
      data: propertyResult,
    });
  } catch (error) {
    console.error("Error fetching property:", error);
    return c.json({ error: "Failed to fetch property" }, 500);
  }
});

propertyRouter.get("/:id/tenants", async (c) => {
  try {
    const user = c.get("user");
    const id = c.req.param("id");

    const propertyRecord = await db
      .select()
      .from(property)
      .where(eq(property.alternative_id, id))
      .limit(1);

    if (propertyRecord.length === 0) {
      return c.json({ error: `Property not found`, related_key: id }, 404);
    }

    if (propertyRecord[0].owner_id !== user!.id) {
      return c.json({ error: "Forbidden" }, 403);
    }

    const lease_records = await db
      .select({ id: lease.alternative_id })
      .from(lease)
      .where(eq(lease.property_id, id))
      .orderBy(desc(lease.start_date));

    const lease_ids = lease_records.map((lease) => lease.id);

    const tenants = await db
      .select({
        alternative_id: tenant.alternative_id,
        first_name: tenant.first_name,
        last_name: tenant.last_name,
        date_of_birth: tenant.date_of_birth,
        national_identity_number: tenant.national_identity_number,
        email: tenant.email,
        phone: tenant.phone,
      })
      .from(tenant)
      .where(inArray(tenant.lease_id, lease_ids));

    return c.json({
      success: true,
      data: tenants,
    });
  } catch (error) {
    console.error("Error fetching tenants:", error);
    return c.json({ error: "Failed to fetch tenants" }, 500);
  }
});

propertyRouter.get("/:id/leases", async (c) => {
  try {
    const user = c.get("user");
    const id = c.req.param("id");

    const propertyRecord = await db
      .select()
      .from(property)
      .where(eq(property.alternative_id, id))
      .limit(1);

    if (propertyRecord.length === 0) {
      return c.json({ error: `Property not found`, related_key: id }, 404);
    }

    if (propertyRecord[0].owner_id !== user!.id) {
      return c.json({ error: "Forbidden" }, 403);
    }

    const leases = await db
      .select({
        lease_id: lease.alternative_id,
        start_date: lease.start_date,
        end_date: lease.end_date,
        rent: lease.rent,
        deposit: lease.deposit,
        currency: lease.currency,
        lease_status: lease_status.code,
      })
      .from(lease)
      .where(eq(lease.property_id, id))
      .innerJoin(lease_status, eq(lease.lease_status, lease_status.id))
      .orderBy(desc(lease.start_date));

    return c.json({
      success: true,
      data: leases,
    });
  } catch (error) {
    console.error("Error fetching leases:", error);
    return c.json({ error: "Failed to fetch leases" }, 500);
  }
});

// Update property
propertyRouter.put("/:id", async (c) => {
  try {
    const user = c.get("user");
    const id = c.req.param("id");
    const body = await c.req.json();

    // Check if property exists and user owns it
    const existingProperty = await db
      .select()
      .from(property)
      .where(eq(property.alternative_id, id))
      .limit(1);

    if (existingProperty.length === 0) {
      return c.json({ error: `Property not found`, related_key: id }, 404);
    }

    if (existingProperty[0].owner_id !== user!.id) {
      return c.json({ error: "Forbidden" }, 403);
    }

    // Validate and filter allowed fields for update
    const allowedFields = [
      'property_name', 'plot_number', 'street_name', 'city', 'state', 'country',
      'bedrooms', 'bathrooms', 'base_rent', 'base_deposit', 'currency', 'listing_date', 'vacant'
    ];

    const updateData: any = {};
    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return c.json({ error: "No valid fields to update" }, 400);
    }

    const updatedProperty = await db
      .update(property)
      .set(updateData)
      .where(eq(property.alternative_id, id))
      .returning();

    return c.json({
      success: true,
      data: updatedProperty[0],
    });
  } catch (error) {
    console.error("Error updating property:", error);
    return c.json({ error: error instanceof Error ? error.message : "Failed to update property" }, 500);
  }
});

// Delete property
propertyRouter.delete("/:id", async (c) => {
  try {
    const user = c.get("user");
    const id = c.req.param("id");

    // Check if property exists and user owns it
    const existingProperty = await db
      .select()
      .from(property)
      .where(eq(property.alternative_id, id))
      .limit(1);

    if (existingProperty.length === 0) {
      return c.json({ error: `Property not found`, related_key: id }, 404);
    }

    if (existingProperty[0].owner_id !== user!.id) {
      return c.json({ error: "Forbidden" }, 403);
    }

    await db
      .delete(property)
      .where(eq(property.alternative_id, id));

    return c.json({
      success: true,
      message: "Property deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting property:", error);
    return c.json({ error: "Failed to delete property" }, 500);
  }
});

export { propertyRouter };
