CREATE TABLE `lease` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`tenant_id` text NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text NOT NULL,
	`rent` integer NOT NULL,
	`deposit` integer NOT NULL,
	`currency` text NOT NULL,
	`lease_status` integer NOT NULL,
	FOREIGN KEY (`tenant_id`) REFERENCES `tenant`(`alternative_id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`lease_status`) REFERENCES `lease_status`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE UNIQUE INDEX `lease_alternative_id_unique` ON `lease` (`alternative_id`);--> statement-breakpoint
CREATE TABLE `lease_status` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`status` text NOT NULL,
	`description` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `lease_status_status_unique` ON `lease_status` (`status`);--> statement-breakpoint
PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_property` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`owner_id` text NOT NULL,
	`property_name` text NOT NULL,
	`plot_number` text NOT NULL,
	`street_name` text NOT NULL,
	`city` text NOT NULL,
	`state` text NOT NULL,
	`country` text NOT NULL,
	`bedrooms` integer NOT NULL,
	`bathrooms` integer NOT NULL,
	`base_rent` integer,
	`base_deposit` integer,
	`currency` text,
	`listing_date` text NOT NULL,
	`vacant` integer DEFAULT false NOT NULL,
	FOREIGN KEY (`owner_id`) REFERENCES `user`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_property`("id", "alternative_id", "owner_id", "property_name", "plot_number", "street_name", "city", "state", "country", "bedrooms", "bathrooms", "base_rent", "base_deposit", "currency", "listing_date", "vacant") SELECT "id", "alternative_id", "owner_id", "property_name", "plot_number", "street_name", "city", "state", "country", "bedrooms", "bathrooms", "base_rent", "base_deposit", "currency", "listing_date", "vacant" FROM `property`;--> statement-breakpoint
DROP TABLE `property`;--> statement-breakpoint
ALTER TABLE `__new_property` RENAME TO `property`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `property_alternative_id_unique` ON `property` (`alternative_id`);--> statement-breakpoint
CREATE TABLE `__new_tenant` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`property_id` text NOT NULL,
	`first_name` text NOT NULL,
	`last_name` text NOT NULL,
	`date_of_birth` text NOT NULL,
	`national_identity_number` text NOT NULL,
	`email` text NOT NULL,
	`phone` text NOT NULL,
	FOREIGN KEY (`property_id`) REFERENCES `property`(`alternative_id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_tenant`("id", "alternative_id", "property_id", "first_name", "last_name", "date_of_birth", "national_identity_number", "email", "phone") SELECT "id", "alternative_id", "property_id", "first_name", "last_name", "date_of_birth", "national_identity_number", "email", "phone" FROM `tenant`;--> statement-breakpoint
DROP TABLE `tenant`;--> statement-breakpoint
ALTER TABLE `__new_tenant` RENAME TO `tenant`;--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_alternative_id_unique` ON `tenant` (`alternative_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_national_identity_number_unique` ON `tenant` (`national_identity_number`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_email_unique` ON `tenant` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `tenant_phone_unique` ON `tenant` (`phone`);