PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_property` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`owner_id` text NOT NULL,
	`property_name` text NOT NULL,
	`plot_number` text NOT NULL,
	`street_name` text NOT NULL,
	`city` text NOT NULL,
	`state` text NOT NULL,
	`country` text NOT NULL,
	`bedrooms` integer NOT NULL,
	`bathrooms` integer NOT NULL,
	`base_rent` integer,
	`base_deposit` integer,
	`currency` text,
	`listing_date` text NOT NULL,
	`vacant` integer DEFAULT false NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_property`("id", "alternative_id", "owner_id", "property_name", "plot_number", "street_name", "city", "state", "country", "bedrooms", "bathrooms", "base_rent", "base_deposit", "currency", "listing_date", "vacant") SELECT "id", "alternative_id", "owner_id", "property_name", "plot_number", "street_name", "city", "state", "country", "bedrooms", "bathrooms", "base_rent", "base_deposit", "currency", "listing_date", "vacant" FROM `property`;--> statement-breakpoint
DROP TABLE `property`;--> statement-breakpoint
ALTER TABLE `__new_property` RENAME TO `property`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `property_alternative_id_unique` ON `property` (`alternative_id`);