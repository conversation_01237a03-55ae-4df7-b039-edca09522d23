import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { user } from "./auth";
import { createInsertSchema, createSelectSchema, createUpdateSchema } from "drizzle-zod";
import z from "zod";

export const property = sqliteTable("property", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  alternative_id: text("alternative_id").notNull().unique(),

  owner_id: text("owner_id").notNull().references(() => user.id),

  property_name: text("property_name").notNull(),
  plot_number: text("plot_number").notNull(),
  street_name: text("street_name").notNull(),
  city: text("city").notNull(),
  state: text("state").notNull(),
  country: text("country").notNull(),
  bedrooms: integer("bedrooms").notNull(),
  bathrooms: integer("bathrooms").notNull(),
  
  base_rent: integer("base_rent"),
  base_deposit: integer("base_deposit"),
  currency: text("currency"),
  
  listing_date: text("listing_date").notNull(),
  vacant: integer("vacant", { mode: "boolean" }).default(false).notNull(),
});


export const propertySelectSchema = createSelectSchema(property);
export const propertyInsertSchema = createInsertSchema(property, {
  alternative_id: z.string().optional(),
  owner_id: z.string().optional(),
});

export const propertyUpdateSchema = createUpdateSchema(property, {
  owner_id: z.string().optional(),
});