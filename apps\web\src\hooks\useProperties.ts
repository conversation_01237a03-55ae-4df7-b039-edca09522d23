import { useQuery } from "@tanstack/react-query";
import { authClient } from "@/lib/auth-client";

export interface Property {
  id: string;
  property_name: string;
  plot_number: string;
  street_name: string;
  city: string;
  state: string;
  country: string;
  bedrooms: number;
  bathrooms: number;
  base_rent?: number;
  base_deposit?: number;
  currency?: string;
  listing_date: string;
  vacant: boolean;
}

export interface PropertyResponse {
  success: boolean;
  data: Property[];
  error?: string;
}

const fetchProperties = async (token: string): Promise<PropertyResponse> => {
  const response = await fetch("/api/property", {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch properties: ${response.statusText}`);
  }

  return response.json();
};

export const useProperties = () => {
  const { data: session } = authClient.useSession();

  return useQuery({
    queryKey: ["properties"],
    queryFn: () => fetchProperties(session?.session.token!),
    enabled: !!session?.session.token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
