{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/schemas.ts"], "sourcesContent": ["import { z } from \"zod\"\n\n// Property Schema\nexport const propertyInsertSchema = z.object({\n  alternative_id: z.string().optional(),\n  owner_id: z.string().optional(),\n  property_name: z.string().min(1, \"Property name is required\"),\n  plot_number: z.string().min(1, \"Plot number is required\"),\n  street_name: z.string().min(1, \"Street name is required\"),\n  city: z.string().min(1, \"City is required\"),\n  state: z.string().min(1, \"State is required\"),\n  country: z.string().min(1, \"Country is required\"),\n  bedrooms: z.number().min(0, \"Bedrooms must be 0 or more\"),\n  bathrooms: z.number().min(0, \"Bathrooms must be 0 or more\"),\n  base_rent: z.number().optional(),\n  base_deposit: z.number().optional(),\n  currency: z.string().optional(),\n  listing_date: z.string().min(1, \"Listing date is required\"),\n  vacant: z.boolean().default(true),\n})\n\n// Lease Schema\nexport const leaseInsertSchema = z.object({\n  alternative_id: z.string().optional(),\n  property_id: z.string().optional(),\n  start_date: z.string().min(1, \"Start date is required\"),\n  end_date: z.string().min(1, \"End date is required\"),\n  rent: z.number().min(0, \"Rent must be 0 or more\"),\n  deposit: z.number().min(0, \"Deposit must be 0 or more\"),\n  currency: z.string().min(1, \"Currency is required\"),\n  lease_status: z.string().min(1, \"Lease status is required\"),\n})\n\n// Tenant Schema\nexport const tenantInsertSchema = z.object({\n  alternative_id: z.string().optional(),\n  lease_id: z.string().optional(),\n  first_name: z.string().min(1, \"First name is required\"),\n  last_name: z.string().min(1, \"Last name is required\"),\n  date_of_birth: z.string().min(1, \"Date of birth is required\"),\n  national_identity_number: z.string().min(1, \"National identity number is required\"),\n  email: z.string().email(\"Valid email is required\"),\n  phone: z.string().min(1, \"Phone number is required\"),\n})\n\nexport type PropertyInsert = z.infer<typeof propertyInsertSchema>\nexport type LeaseInsert = z.infer<typeof leaseInsertSchema>\nexport type TenantInsert = z.infer<typeof tenantInsertSchema>\n"], "names": [], "mappings": ";;;;;AAAA;;AAGO,MAAM,uBAAuB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,gBAAgB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,eAAe,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,WAAW,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,WAAW,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,cAAc,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,cAAc,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,QAAQ,6NAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAC9B;AAGO,MAAM,oBAAoB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,gBAAgB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,cAAc,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAClC;AAGO,MAAM,qBAAqB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,gBAAgB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,YAAY,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,WAAW,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,eAAe,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,0BAA0B,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5C,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/actions.ts"], "sourcesContent": ["\"use server\"\n\nimport { z } from \"zod\"\nimport { propertyInsertSchema, leaseInsertSchema, tenantInsertSchema } from \"./schemas\"\nimport { headers } from \"next/headers\"\n\n// Server action for property validation and saving\nexport async function createProperty(formData: FormData) {\n  try {\n    const data = {\n      property_name: formData.get(\"property_name\") as string,\n      plot_number: formData.get(\"plot_number\") as string,\n      street_name: formData.get(\"street_name\") as string,\n      city: formData.get(\"city\") as string,\n      state: formData.get(\"state\") as string,\n      country: formData.get(\"country\") as string,\n      bedrooms: Number(formData.get(\"bedrooms\")) || 0,\n      bathrooms: Number(formData.get(\"bathrooms\")) || 0,\n      base_rent: Number(formData.get(\"base_rent\")) || undefined,\n      base_deposit: Number(formData.get(\"base_deposit\")) || undefined,\n      currency: formData.get(\"currency\") as string,\n      listing_date: formData.get(\"listing_date\") as string,\n      vacant: formData.get(\"vacant\") === \"on\",\n    }\n\n    // Validate with Zod schema\n    const validatedData = propertyInsertSchema.parse(data)\n\n    // Send POST request to backend API with headers forwarded for authentication\n    const headersList = await headers()\n    const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/property`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        // Forward all headers including cookies for authentication\n        \"Cookie\": headersList.get(\"cookie\") || \"\",\n      },\n      body: JSON.stringify(validatedData),\n    })\n\n    if (!response.ok) {\n      let errorMessage = `Failed to create property: ${response.statusText}`\n      try {\n        const errorData = await response.json() as { error?: string }\n        if (errorData.error) {\n          errorMessage = errorData.error\n        }\n      } catch {\n        // If JSON parsing fails, use the default error message\n      }\n      return {\n        success: false,\n        error: errorMessage\n      }\n    }\n\n    const result = await response.json() as { success: boolean; data?: any; error?: string }\n\n    if (result.success) {\n      return { success: true, data: result.data }\n    } else {\n      return { success: false, error: result.error || \"Failed to create property\" }\n    }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, errors: error.flatten().fieldErrors }\n    }\n    console.error(\"Error creating property:\", error)\n    return { success: false, error: \"Failed to create property\" }\n  }\n}\n\n// Server action for lease validation and saving\nexport async function createLease(formData: FormData) {\n  try {\n    const data = {\n      property_id: formData.get(\"property_id\") as string,\n      start_date: formData.get(\"start_date\") as string,\n      end_date: formData.get(\"end_date\") as string,\n      rent: Number(formData.get(\"rent\")) || 0,\n      deposit: Number(formData.get(\"deposit\")) || 0,\n      currency: formData.get(\"currency\") as string,\n      lease_status: formData.get(\"lease_status\") as string,\n    }\n\n    // Validate with Zod schema\n    const validatedData = leaseInsertSchema.parse(data)\n\n    // Here you would typically save to database\n    console.log(\"Creating lease:\", validatedData)\n\n    return { success: true, data: validatedData }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, errors: error.flatten().fieldErrors }\n    }\n    return { success: false, error: \"Failed to create lease\" }\n  }\n}\n\n// Server action for tenant validation and saving\nexport async function createTenant(formData: FormData) {\n  try {\n    const data = {\n      lease_id: formData.get(\"lease_id\") as string,\n      first_name: formData.get(\"first_name\") as string,\n      last_name: formData.get(\"last_name\") as string,\n      date_of_birth: formData.get(\"date_of_birth\") as string,\n      national_identity_number: formData.get(\"national_identity_number\") as string,\n      email: formData.get(\"email\") as string,\n      phone: formData.get(\"phone\") as string,\n    }\n\n    // Validate with Zod schema\n    const validatedData = tenantInsertSchema.parse(data)\n\n    // Here you would typically save to database\n    console.log(\"Creating tenant:\", validatedData)\n\n    return { success: true, data: validatedData }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, errors: error.flatten().fieldErrors }\n    }\n    return { success: false, error: \"Failed to create tenant\" }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;;AAGO,eAAe,eAAe,QAAkB;IACrD,IAAI;QACF,MAAM,OAAO;YACX,eAAe,SAAS,GAAG,CAAC;YAC5B,aAAa,SAAS,GAAG,CAAC;YAC1B,aAAa,SAAS,GAAG,CAAC;YAC1B,MAAM,SAAS,GAAG,CAAC;YACnB,OAAO,SAAS,GAAG,CAAC;YACpB,SAAS,SAAS,GAAG,CAAC;YACtB,UAAU,OAAO,SAAS,GAAG,CAAC,gBAAgB;YAC9C,WAAW,OAAO,SAAS,GAAG,CAAC,iBAAiB;YAChD,WAAW,OAAO,SAAS,GAAG,CAAC,iBAAiB;YAChD,cAAc,OAAO,SAAS,GAAG,CAAC,oBAAoB;YACtD,UAAU,SAAS,GAAG,CAAC;YACvB,cAAc,SAAS,GAAG,CAAC;YAC3B,QAAQ,SAAS,GAAG,CAAC,cAAc;QACrC;QAEA,2BAA2B;QAC3B,MAAM,gBAAgB,oIAAA,CAAA,uBAAoB,CAAC,KAAK,CAAC;QAEjD,6EAA6E;QAC7E,MAAM,cAAc,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAO,AAAD;QAChC,MAAM,WAAW,MAAM,MAAM,6DAAsC,aAAa,CAAC,EAAE;YACjF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,2DAA2D;gBAC3D,UAAU,YAAY,GAAG,CAAC,aAAa;YACzC;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,IAAI,eAAe,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;YACtE,IAAI;gBACF,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,IAAI,UAAU,KAAK,EAAE;oBACnB,eAAe,UAAU,KAAK;gBAChC;YACF,EAAE,OAAM;YACN,uDAAuD;YACzD;YACA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAM,MAAM,OAAO,IAAI;YAAC;QAC5C,OAAO;YACL,OAAO;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAK,IAAI;YAA4B;QAC9E;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6NAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO;gBAAE,SAAS;gBAAO,QAAQ,MAAM,OAAO,GAAG,WAAW;YAAC;QAC/D;QACA,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4B;IAC9D;AACF;AAGO,eAAe,YAAY,QAAkB;IAClD,IAAI;QACF,MAAM,OAAO;YACX,aAAa,SAAS,GAAG,CAAC;YAC1B,YAAY,SAAS,GAAG,CAAC;YACzB,UAAU,SAAS,GAAG,CAAC;YACvB,MAAM,OAAO,SAAS,GAAG,CAAC,YAAY;YACtC,SAAS,OAAO,SAAS,GAAG,CAAC,eAAe;YAC5C,UAAU,SAAS,GAAG,CAAC;YACvB,cAAc,SAAS,GAAG,CAAC;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,gBAAgB,oIAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;QAE9C,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,OAAO;YAAE,SAAS;YAAM,MAAM;QAAc;IAC9C,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6NAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO;gBAAE,SAAS;gBAAO,QAAQ,MAAM,OAAO,GAAG,WAAW;YAAC;QAC/D;QACA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyB;IAC3D;AACF;AAGO,eAAe,aAAa,QAAkB;IACnD,IAAI;QACF,MAAM,OAAO;YACX,UAAU,SAAS,GAAG,CAAC;YACvB,YAAY,SAAS,GAAG,CAAC;YACzB,WAAW,SAAS,GAAG,CAAC;YACxB,eAAe,SAAS,GAAG,CAAC;YAC5B,0BAA0B,SAAS,GAAG,CAAC;YACvC,OAAO,SAAS,GAAG,CAAC;YACpB,OAAO,SAAS,GAAG,CAAC;QACtB;QAEA,2BAA2B;QAC3B,MAAM,gBAAgB,oIAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC;QAE/C,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,OAAO;YAAE,SAAS;YAAM,MAAM;QAAc;IAC9C,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6NAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO;gBAAE,SAAS;gBAAO,QAAQ,MAAM,OAAO,GAAG,WAAW;YAAC;QAC/D;QACA,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;AACF;;;IAvHsB;IAkEA;IA4BA;;AA9FA,oTAAA;AAkEA,oTAAA;AA4BA,oTAAA", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/.next-internal/server/app/properties/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {createProperty as '40b4548ebdb37f82eaab6668fbba538619d3447911'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/property-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PropertyForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call PropertyForm() from the server but PropertyForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/property-form.tsx <module evaluation>\",\n    \"PropertyForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,0TAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2EACA", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/property-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PropertyForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call PropertyForm() from the server but PropertyForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/property-form.tsx\",\n    \"PropertyForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,0TAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uDACA", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,0OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,8PAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/app/properties/page.tsx"], "sourcesContent": ["import { PropertyForm } from \"@/components/property-form\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { ArrowLeft, Building } from \"lucide-react\"\nimport Link from \"next/link\"\n\nexport default function PropertiesPage() {\n  return (\n    <main className=\"min-h-screen bg-background py-8\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center gap-4 mb-8\">\n          <Link href=\"/\">\n            <Button variant=\"outline\" size=\"sm\">\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Back to Dashboard\n            </Button>\n          </Link>\n          <div className=\"flex items-center gap-2\">\n            <Building className=\"w-6 h-6 text-primary\" />\n            <h1 className=\"text-3xl font-bold\">Property Management</h1>\n          </div>\n        </div>\n\n        <PropertyForm />\n      </div>\n    </main>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,mTAAC;QAAK,WAAU;kBACd,cAAA,mTAAC;YAAI,WAAU;;8BACb,mTAAC;oBAAI,WAAU;;sCACb,mTAAC,iOAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,mTAAC,iJAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,mTAAC,iSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAI1C,mTAAC;4BAAI,WAAU;;8CACb,mTAAC,2RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,mTAAC;oCAAG,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;8BAIvC,mTAAC,qJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}]}