import { useActionState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileText, Plus } from "lucide-react"
import { createLease } from "@/lib/actions"

const currencies = ["USD", "EUR", "GBP", "CAD", "AUD"]
const leaseStatuses = ["active", "pending", "expired", "terminated"]

interface LeaseFormProps {
  properties?: Array<{ id: string; name: string }>
}

export function LeaseForm({ properties = [] }: LeaseFormProps) {
  const [state, formAction] = useActionState(createLease, null)

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Create Lease Agreement
        </CardTitle>
        <CardDescription>Create a new lease agreement for your property</CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-6">
          {/* Property Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Property Selection</h3>
            <div className="space-y-2">
              <Label htmlFor="property_id">Select Property</Label>
              <Select name="property_id" required>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a property" />
                </SelectTrigger>
                <SelectContent>
                  {properties.length > 0 ? (
                    properties.map((property) => (
                      <SelectItem key={property.id} value={property.id}>
                        {property.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      No properties available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {state?.errors?.property_id && <p className="text-sm text-red-500">{state.errors.property_id[0]}</p>}
            </div>
          </div>

          {/* Lease Duration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Lease Duration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input id="start_date" name="start_date" type="date" required />
                {state?.errors?.start_date && <p className="text-sm text-red-500">{state.errors.start_date[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="end_date">End Date</Label>
                <Input id="end_date" name="end_date" type="date" required />
                {state?.errors?.end_date && <p className="text-sm text-red-500">{state.errors.end_date[0]}</p>}
              </div>
            </div>
          </div>

          {/* Financial Terms */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Financial Terms</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="rent">Monthly Rent</Label>
                <Input id="rent" name="rent" type="number" min="0" step="0.01" placeholder="0.00" required />
                {state?.errors?.rent && <p className="text-sm text-red-500">{state.errors.rent[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="deposit">Security Deposit</Label>
                <Input id="deposit" name="deposit" type="number" min="0" step="0.01" placeholder="0.00" required />
                {state?.errors?.deposit && <p className="text-sm text-red-500">{state.errors.deposit[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select name="currency" defaultValue="USD" required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency} value={currency}>
                        {currency}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state?.errors?.currency && <p className="text-sm text-red-500">{state.errors.currency[0]}</p>}
              </div>
            </div>
          </div>

          {/* Lease Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Lease Status</h3>
            <div className="space-y-2">
              <Label htmlFor="lease_status">Status</Label>
              <Select name="lease_status" defaultValue="active" required>
                <SelectTrigger>
                  <SelectValue placeholder="Select lease status" />
                </SelectTrigger>
                <SelectContent>
                  {leaseStatuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {state?.errors?.lease_status && <p className="text-sm text-red-500">{state.errors.lease_status[0]}</p>}
            </div>
          </div>

          {/* Success/Error Messages */}
          {state?.success && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">Lease agreement created successfully!</p>
            </div>
          )}
          {state?.error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">{state.error}</p>
            </div>
          )}

          <Button type="submit" className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            Create Lease Agreement
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
