import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { lease } from "./lease";
import { createInsertSchema, createSelectSchema, createUpdateSchema } from "drizzle-zod";
import { z } from "zod";

export const tenant = sqliteTable("tenant", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  alternative_id: text("alternative_id").notNull().unique(),
  lease_id: text("lease_id").notNull().references(() => lease.alternative_id),
  first_name: text("first_name").notNull(),
  last_name: text("last_name").notNull(),
  date_of_birth: text("date_of_birth").notNull(),
  national_identity_number: text("national_identity_number").notNull(),
  email: text("email").notNull(),
  phone: text("phone").notNull(),
});

export const tenantSelectSchema = createSelectSchema(tenant);
export const tenantInsertSchema = createInsertSchema(tenant, {
  alternative_id: z.string().optional(),
  lease_id: z.string().optional(),
});

export const tenantUpdateSchema = createUpdateSchema(tenant, {
  lease_id: z.string().optional(),
});
