{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,mTAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,mTAAC,gQAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;AACA;AAAA;AAAA;AAEA;;;;;AAEA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,mTAAC,kQAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mTAAC,kQAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mTAAC,kQAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,mTAAC,kQAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,mTAAC,kQAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,mTAAC,ySAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,mTAAC,kQAAA,CAAA,SAAsB;kBACrB,cAAA,mTAAC,kQAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,mTAAC;;;;;8BACD,mTAAC,kQAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,mTAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,mTAAC,kQAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,mTAAC,kQAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,mTAAC;gBAAK,WAAU;0BACd,cAAA,mTAAC,kQAAA,CAAA,gBAA6B;8BAC5B,cAAA,mTAAC,yRAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,mTAAC,kQAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,mTAAC,kQAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,mTAAC,kQAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mTAAC,qSAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,mTAAC,kQAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mTAAC,ySAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,mTAAC,sQAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,mTAAC,sQAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,mTAAC,yRAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/actions.ts"], "sourcesContent": ["\"use server\"\n\nimport { z } from \"zod\"\nimport { propertyInsertSchema, leaseInsertSchema, tenantInsertSchema } from \"./schemas\"\nimport { headers } from \"next/headers\"\n\n// Server action for property validation and saving\nexport async function createProperty(formData: FormData) {\n  try {\n    const data = {\n      property_name: formData.get(\"property_name\") as string,\n      plot_number: formData.get(\"plot_number\") as string,\n      street_name: formData.get(\"street_name\") as string,\n      city: formData.get(\"city\") as string,\n      state: formData.get(\"state\") as string,\n      country: formData.get(\"country\") as string,\n      bedrooms: Number(formData.get(\"bedrooms\")) || 0,\n      bathrooms: Number(formData.get(\"bathrooms\")) || 0,\n      base_rent: Number(formData.get(\"base_rent\")) || undefined,\n      base_deposit: Number(formData.get(\"base_deposit\")) || undefined,\n      currency: formData.get(\"currency\") as string,\n      listing_date: formData.get(\"listing_date\") as string,\n      vacant: formData.get(\"vacant\") === \"on\",\n    }\n\n    // Validate with Zod schema\n    const validatedData = propertyInsertSchema.parse(data)\n\n    // Send POST request to backend API with headers forwarded for authentication\n    const headersList = await headers()\n    const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_URL}/api/property`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        // Forward all headers including cookies for authentication\n        \"Cookie\": headersList.get(\"cookie\") || \"\",\n      },\n      body: JSON.stringify(validatedData),\n    })\n\n    if (!response.ok) {\n      let errorMessage = `Failed to create property: ${response.statusText}`\n      try {\n        const errorData = await response.json()\n        if (errorData.error) {\n          errorMessage = errorData.error\n        }\n      } catch {\n        // If JSON parsing fails, use the default error message\n      }\n      return {\n        success: false,\n        error: errorMessage\n      }\n    }\n\n    const result = await response.json()\n\n    if (result.success) {\n      return { success: true, data: result.data }\n    } else {\n      return { success: false, error: result.error || \"Failed to create property\" }\n    }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, errors: error.flatten().fieldErrors }\n    }\n    console.error(\"Error creating property:\", error)\n    return { success: false, error: \"Failed to create property\" }\n  }\n}\n\n// Server action for lease validation and saving\nexport async function createLease(formData: FormData) {\n  try {\n    const data = {\n      property_id: formData.get(\"property_id\") as string,\n      start_date: formData.get(\"start_date\") as string,\n      end_date: formData.get(\"end_date\") as string,\n      rent: Number(formData.get(\"rent\")) || 0,\n      deposit: Number(formData.get(\"deposit\")) || 0,\n      currency: formData.get(\"currency\") as string,\n      lease_status: formData.get(\"lease_status\") as string,\n    }\n\n    // Validate with Zod schema\n    const validatedData = leaseInsertSchema.parse(data)\n\n    // Here you would typically save to database\n    console.log(\"Creating lease:\", validatedData)\n\n    return { success: true, data: validatedData }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, errors: error.flatten().fieldErrors }\n    }\n    return { success: false, error: \"Failed to create lease\" }\n  }\n}\n\n// Server action for tenant validation and saving\nexport async function createTenant(formData: FormData) {\n  try {\n    const data = {\n      lease_id: formData.get(\"lease_id\") as string,\n      first_name: formData.get(\"first_name\") as string,\n      last_name: formData.get(\"last_name\") as string,\n      date_of_birth: formData.get(\"date_of_birth\") as string,\n      national_identity_number: formData.get(\"national_identity_number\") as string,\n      email: formData.get(\"email\") as string,\n      phone: formData.get(\"phone\") as string,\n    }\n\n    // Validate with Zod schema\n    const validatedData = tenantInsertSchema.parse(data)\n\n    // Here you would typically save to database\n    console.log(\"Creating tenant:\", validatedData)\n\n    return { success: true, data: validatedData }\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return { success: false, errors: error.flatten().fieldErrors }\n    }\n    return { success: false, error: \"Failed to create tenant\" }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAOsB,iBAAA,WAAA,GAAA,CAAA,GAAA,2RAAA,CAAA,wBAAA,EAAA,8CAAA,2RAAA,CAAA,aAAA,EAAA,KAAA,GAAA,2RAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/property-form.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useActionState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Building, Plus } from \"lucide-react\"\nimport { createProperty } from \"@/lib/actions\"\n\nconst currencies = [\"USD\", \"EUR\", \"GBP\", \"CAD\", \"AUD\"]\n\nexport function PropertyForm() {\n  const [state, formAction] = useActionState(createProperty, null)\n  const [isVacant, setIsVacant] = useState(true)\n\n  return (\n    <Card className=\"max-w-4xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Building className=\"w-5 h-5\" />\n          Add Property\n        </CardTitle>\n        <CardDescription>Add a new rental property to your portfolio</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form action={formAction} className=\"space-y-6\">\n          {/* Basic Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Basic Information</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"property_name\">Property Name</Label>\n                <Input id=\"property_name\" name=\"property_name\" placeholder=\"e.g., Sunset Apartments\" required />\n                {state?.errors?.property_name && (\n                  <p className=\"text-sm text-red-500\">{state.errors.property_name[0]}</p>\n                )}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"plot_number\">Plot Number</Label>\n                <Input id=\"plot_number\" name=\"plot_number\" placeholder=\"e.g., 123\" required />\n                {state?.errors?.plot_number && <p className=\"text-sm text-red-500\">{state.errors.plot_number[0]}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Location */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Location</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"street_name\">Street Name</Label>\n                <Input id=\"street_name\" name=\"street_name\" placeholder=\"e.g., Main Street\" required />\n                {state?.errors?.street_name && <p className=\"text-sm text-red-500\">{state.errors.street_name[0]}</p>}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"city\">City</Label>\n                <Input id=\"city\" name=\"city\" placeholder=\"e.g., New York\" required />\n                {state?.errors?.city && <p className=\"text-sm text-red-500\">{state.errors.city[0]}</p>}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"state\">State</Label>\n                <Input id=\"state\" name=\"state\" placeholder=\"e.g., NY\" required />\n                {state?.errors?.state && <p className=\"text-sm text-red-500\">{state.errors.state[0]}</p>}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"country\">Country</Label>\n                <Input id=\"country\" name=\"country\" placeholder=\"e.g., USA\" required />\n                {state?.errors?.country && <p className=\"text-sm text-red-500\">{state.errors.country[0]}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Property Details */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Property Details</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"bedrooms\">Bedrooms</Label>\n                <Input id=\"bedrooms\" name=\"bedrooms\" type=\"number\" min=\"0\" defaultValue=\"0\" />\n                {state?.errors?.bedrooms && <p className=\"text-sm text-red-500\">{state.errors.bedrooms[0]}</p>}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"bathrooms\">Bathrooms</Label>\n                <Input id=\"bathrooms\" name=\"bathrooms\" type=\"number\" min=\"0\" defaultValue=\"0\" />\n                {state?.errors?.bathrooms && <p className=\"text-sm text-red-500\">{state.errors.bathrooms[0]}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Financial Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Financial Information</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"base_rent\">Base Rent</Label>\n                <Input id=\"base_rent\" name=\"base_rent\" type=\"number\" min=\"0\" step=\"0.01\" placeholder=\"0.00\" />\n                {state?.errors?.base_rent && <p className=\"text-sm text-red-500\">{state.errors.base_rent[0]}</p>}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"base_deposit\">Base Deposit</Label>\n                <Input id=\"base_deposit\" name=\"base_deposit\" type=\"number\" min=\"0\" step=\"0.01\" placeholder=\"0.00\" />\n                {state?.errors?.base_deposit && <p className=\"text-sm text-red-500\">{state.errors.base_deposit[0]}</p>}\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"currency\">Currency</Label>\n                <Select name=\"currency\" defaultValue=\"USD\">\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select currency\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {currencies.map((currency) => (\n                      <SelectItem key={currency} value={currency}>\n                        {currency}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {state?.errors?.currency && <p className=\"text-sm text-red-500\">{state.errors.currency[0]}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Additional Information */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">Additional Information</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"listing_date\">Listing Date</Label>\n                <Input\n                  id=\"listing_date\"\n                  name=\"listing_date\"\n                  type=\"date\"\n                  defaultValue={new Date().toISOString().split(\"T\")[0]}\n                  required\n                />\n                {state?.errors?.listing_date && <p className=\"text-sm text-red-500\">{state.errors.listing_date[0]}</p>}\n              </div>\n              <div className=\"flex items-center space-x-2 pt-8\">\n                <Checkbox id=\"vacant\" name=\"vacant\" checked={isVacant} onCheckedChange={setIsVacant} />\n                <Label htmlFor=\"vacant\">Property is currently vacant</Label>\n              </div>\n            </div>\n          </div>\n\n          {/* Success/Error Messages */}\n          {state?.success && (\n            <div className=\"p-4 bg-green-50 border border-green-200 rounded-md\">\n              <p className=\"text-green-800\">Property created successfully!</p>\n            </div>\n          )}\n          {state?.error && (\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-red-800\">{state.error}</p>\n            </div>\n          )}\n\n          <Button type=\"submit\" className=\"w-full\">\n            <Plus className=\"w-4 h-4 mr-2\" />\n            Add Property\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAXA;;;;;;;;;;;;AAaA,MAAM,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;CAAM;AAE/C,SAAS;IACd,MAAM,CAAC,OAAO,WAAW,GAAG,CAAA,GAAA,0QAAA,CAAA,iBAAc,AAAD,EAAE,iKAAA,CAAA,iBAAc,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0QAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE,mTAAC,+IAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mTAAC,+IAAA,CAAA,aAAU;;kCACT,mTAAC,+IAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,mTAAC,2RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGlC,mTAAC,+IAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAEnB,mTAAC,+IAAA,CAAA,cAAW;0BACV,cAAA,mTAAC;oBAAK,QAAQ;oBAAY,WAAU;;sCAElC,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAgB,MAAK;oDAAgB,aAAY;oDAA0B,QAAQ;;;;;;gDAC5F,OAAO,QAAQ,+BACd,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,aAAa,CAAC,EAAE;;;;;;;;;;;;sDAGtE,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAc,MAAK;oDAAc,aAAY;oDAAY,QAAQ;;;;;;gDAC1E,OAAO,QAAQ,6BAAe,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAMrG,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAc,MAAK;oDAAc,aAAY;oDAAoB,QAAQ;;;;;;gDAClF,OAAO,QAAQ,6BAAe,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE;;;;;;;;;;;;sDAEjG,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAO,MAAK;oDAAO,aAAY;oDAAiB,QAAQ;;;;;;gDACjE,OAAO,QAAQ,sBAAQ,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;sDAEnF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAQ,MAAK;oDAAQ,aAAY;oDAAW,QAAQ;;;;;;gDAC7D,OAAO,QAAQ,uBAAS,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;sDAErF,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAU,MAAK;oDAAU,aAAY;oDAAY,QAAQ;;;;;;gDAClE,OAAO,QAAQ,yBAAW,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,OAAO,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAM7F,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAW,MAAK;oDAAW,MAAK;oDAAS,KAAI;oDAAI,cAAa;;;;;;gDACvE,OAAO,QAAQ,0BAAY,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;sDAE3F,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAY,MAAK;oDAAY,MAAK;oDAAS,KAAI;oDAAI,cAAa;;;;;;gDACzE,OAAO,QAAQ,2BAAa,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAMjG,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAY,MAAK;oDAAY,MAAK;oDAAS,KAAI;oDAAI,MAAK;oDAAO,aAAY;;;;;;gDACpF,OAAO,QAAQ,2BAAa,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE;;;;;;;;;;;;sDAE7F,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,mTAAC,gJAAA,CAAA,QAAK;oDAAC,IAAG;oDAAe,MAAK;oDAAe,MAAK;oDAAS,KAAI;oDAAI,MAAK;oDAAO,aAAY;;;;;;gDAC1F,OAAO,QAAQ,8BAAgB,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,YAAY,CAAC,EAAE;;;;;;;;;;;;sDAEnG,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,mTAAC,iJAAA,CAAA,SAAM;oDAAC,MAAK;oDAAW,cAAa;;sEACnC,mTAAC,iJAAA,CAAA,gBAAa;sEACZ,cAAA,mTAAC,iJAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,mTAAC,iJAAA,CAAA,gBAAa;sEACX,WAAW,GAAG,CAAC,CAAC,yBACf,mTAAC,iJAAA,CAAA,aAAU;oEAAgB,OAAO;8EAC/B;mEADc;;;;;;;;;;;;;;;;gDAMtB,OAAO,QAAQ,0BAAY,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAM/F,mTAAC;4BAAI,WAAU;;8CACb,mTAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,mTAAC;oCAAI,WAAU;;sDACb,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,mTAAC,gJAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oDACpD,QAAQ;;;;;;gDAET,OAAO,QAAQ,8BAAgB,mTAAC;oDAAE,WAAU;8DAAwB,MAAM,MAAM,CAAC,YAAY,CAAC,EAAE;;;;;;;;;;;;sDAEnG,mTAAC;4CAAI,WAAU;;8DACb,mTAAC,mJAAA,CAAA,WAAQ;oDAAC,IAAG;oDAAS,MAAK;oDAAS,SAAS;oDAAU,iBAAiB;;;;;;8DACxE,mTAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;wBAM7B,OAAO,yBACN,mTAAC;4BAAI,WAAU;sCACb,cAAA,mTAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;wBAGjC,OAAO,uBACN,mTAAC;4BAAI,WAAU;sCACb,cAAA,mTAAC;gCAAE,WAAU;0CAAgB,MAAM,KAAK;;;;;;;;;;;sCAI5C,mTAAC,iJAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;;8CAC9B,mTAAC,mRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}]}