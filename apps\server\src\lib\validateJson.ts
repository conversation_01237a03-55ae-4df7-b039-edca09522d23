import { z } from "zod";

// Helper function for validation
export const validateJson = async (c: any, schema: z.ZodSchema) => {
  try {
    const body = await c.req.json();
    return schema.parse(body);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation error:", error.issues);      
      return c.json({ error: error.issues.map((e) => {
        return {
          field: e.path.join("."),
          message: e.message,
        };
      }) }, 400);
    }
    console.error("Error parsing JSON:", error);
    throw error;
  }
};


export const validateOutputJson = async (c: any, data: any, schema: z.ZodSchema) => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return c.json({ error: error.issues.map((e) => {
        return {
          field: e.path.join("."),
          message: e.message,
        };
      }) }, 400);
    }
    throw error;
  }
};