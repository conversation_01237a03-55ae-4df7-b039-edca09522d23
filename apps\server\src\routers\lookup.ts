import { Hono } from "hono";
import type { AuthType } from "@/lib/auth";
import { lease_status } from "@/db/schema/lease";
import { db } from "@/db";

const lookupRouter = new Hono<{ Bindings: CloudflareBindings, Variables: AuthType }>();

lookupRouter.get("/lease-status", async (c) => {
    try {
        const leaseStatus = await db
            .select({
                code: lease_status.code,
                description: lease_status.description,
            })
            .from(lease_status);

        return c.json({
            success: true,
            data: leaseStatus,
        });
    } catch (error) {
        console.error("Error fetching lease status:", error);
        return c.json({ error: "Failed to fetch lease status" }, 500);
    }
});

export { lookupRouter }