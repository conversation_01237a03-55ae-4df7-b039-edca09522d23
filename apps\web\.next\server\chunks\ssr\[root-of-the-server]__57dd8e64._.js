module.exports = {

"[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/6a888_@tanstack_query-devtools_build_42837ad0._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/EDEL3XIZ.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/6a888_@tanstack_query-devtools_build_7a9057df._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.bun/@tanstack+query-devtools@5.84.0/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/RN252AT2.js [app-ssr] (ecmascript)");
    });
});
}}),
"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}}),

};