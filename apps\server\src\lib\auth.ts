import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "../db";
import { user, account, session,verification } from "../db/schema/auth";
import { env } from "cloudflare:workers";
import type { propertySelectSchema } from "../db/schema/property";
import type { z } from "zod";
import type { leaseSelectSchema } from "../db/schema/lease";

export const auth = betterAuth({
   database: drizzleAdapter(db, {
    
    provider: "sqlite",    
    schema: {
      user,
      account,
      session,
      verification,
    },
  }),
  trustedOrigins: [env.CORS_ORIGIN],
  emailAndPassword: {
    enabled: true,
  },
  secret: env.BETTER_AUTH_SECRET,
  baseURL: env.BETTER_AUTH_URL,
});

export type AuthType = {
    user: typeof auth.$Infer.Session.user | null
    session: typeof auth.$Infer.Session.session | null
    property: z.infer<typeof propertySelectSchema> | null
    lease: z.infer<typeof leaseSelectSchema> | null
}