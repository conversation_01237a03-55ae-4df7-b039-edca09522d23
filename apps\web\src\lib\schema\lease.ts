import { sqliteTable, integer, text, numeric } from "drizzle-orm/sqlite-core";
import { property } from "./property";
import { createInsertSchema, createSelectSchema, createUpdateSchema } from "drizzle-zod";
import { z } from "zod";

export const lease = sqliteTable("lease", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  alternative_id: text("alternative_id").notNull().unique(),
  property_id: text("property_id").notNull().references(() => property.alternative_id),
  start_date: text("start_date").notNull(),
  end_date: text("end_date").notNull(),
  rent: numeric("rent", { mode: 'number',  }).notNull(),
  deposit: numeric("deposit", { mode: 'number' }).notNull(),
  currency: text("currency").notNull(),
  lease_status: integer("lease_status").notNull().references(() => lease_status.id),
});

export const lease_status = sqliteTable("lease_status", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  description: text("description").notNull(),
});


export const leaseSelectSchema = createSelectSchema(lease);
export const leaseUpdateSchema = createUpdateSchema(lease, {
  lease_status: z.string().min(1),
});
export const leaseInsertSchema = createInsertSchema(lease, {
  alternative_id: z.string().optional(),
  lease_status: z.string().min(1),
  property_id: z.string().optional(),
});