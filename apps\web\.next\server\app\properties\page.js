const CHUNK_PUBLIC_PATH = "server/app/properties/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/466aa_next_dist_f38f6121._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_src_app_3a9022d1._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__90e219f7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__bun_dc364317._.js");
runtime.loadChunk("server/chunks/ssr/466aa_next_dist_client_components_forbidden-error_670149d6.js");
runtime.loadChunk("server/chunks/ssr/466aa_next_dist_client_components_unauthorized-error_7618d023.js");
runtime.loadChunk("server/chunks/ssr/466aa_next_f60a8532._.js");
runtime.loadChunk("server/chunks/ssr/0aceb_zod_v4_e2ed5598._.js");
runtime.loadChunk("server/chunks/ssr/e3ecf_tailwind-merge_dist_bundle-mjs_mjs_a0d57512._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__bun_ad949db2._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_68defb67._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/.next-internal/server/app/properties/page/actions.js { ACTIONS_MODULE0 => \"[project]/apps/web/src/lib/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/esm/build/templates/app-page.js?page=/properties/page { METADATA_0 => \"[project]/apps/web/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/web/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/web/src/app/properties/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/esm/build/templates/app-page.js?page=/properties/page { METADATA_0 => \"[project]/apps/web/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/web/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/web/src/app/properties/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
