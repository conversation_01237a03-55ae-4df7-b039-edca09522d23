{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "wrangler deploy --dry-run", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "wrangler dev --port=3000", "start": "wrangler dev", "deploy": "wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"@libsql/client": "^0.15.9", "better-auth": "^1.3.4", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.3", "hono": "^4.8.2", "zod": "^4.0.2"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "drizzle-kit": "^0.31.2", "wrangler": "^4.23.0", "@types/node": "^22.13.11"}}