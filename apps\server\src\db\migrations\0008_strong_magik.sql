PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_lease` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`alternative_id` text NOT NULL,
	`property_id` text NOT NULL,
	`start_date` text NOT NULL,
	`end_date` text NOT NULL,
	`rent` numeric NOT NULL,
	`deposit` numeric NOT NULL,
	`currency` text NOT NULL,
	`lease_status` integer NOT NULL,
	FOREIGN KEY (`property_id`) REFERENCES `property`(`alternative_id`) ON UPDATE no action ON DELETE no action,
	FOREIGN KEY (`lease_status`) REFERENCES `lease_status`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_lease`("id", "alternative_id", "property_id", "start_date", "end_date", "rent", "deposit", "currency", "lease_status") SELECT "id", "alternative_id", "property_id", "start_date", "end_date", "rent", "deposit", "currency", "lease_status" FROM `lease`;--> statement-breakpoint
DROP TABLE `lease`;--> statement-breakpoint
ALTER TABLE `__new_lease` RENAME TO `lease`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `lease_alternative_id_unique` ON `lease` (`alternative_id`);