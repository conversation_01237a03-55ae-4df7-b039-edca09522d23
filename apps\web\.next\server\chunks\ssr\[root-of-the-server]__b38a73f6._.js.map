{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: React.ComponentProps<typeof NextThemesProvider>) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,mTAAC,8NAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as Son<PERSON>, type ToasterProps } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,8NAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,mTAAC,8MAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/providers.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\nimport { useState } from \"react\";\nimport { ThemeProvider } from \"./theme-provider\";\nimport { Toaster } from \"./ui/sonner\";\n\nexport default function Providers({\n  children\n}: {\n  children: React.ReactNode\n}) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 5 * 60 * 1000, // 5 minutes\n            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n            retry: 1,\n            refetchOnWindowFocus: false,\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <ThemeProvider\n        attribute=\"class\"\n        defaultTheme=\"system\"\n        enableSystem\n        disableTransitionOnChange\n      >\n        {children}\n        <Toaster richColors />\n        <ReactQueryDevtools initialIsOpen={false} />\n      </ThemeProvider>\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS,UAAU,EAChC,QAAQ,EAGT;IACC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,0QAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,qPAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,WAAW,IAAI,KAAK;oBACpB,QAAQ,KAAK,KAAK;oBAClB,OAAO;oBACP,sBAAsB;gBACxB;YACF;QACF;IAGJ,qBACE,mTAAC,mRAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,mTAAC,sJAAA,CAAA,gBAAa;YACZ,WAAU;YACV,cAAa;YACb,YAAY;YACZ,yBAAyB;;gBAExB;8BACD,mTAAC,iJAAA,CAAA,UAAO;oBAAC,UAAU;;;;;;8BACnB,mTAAC,6RAAA,CAAA,qBAAkB;oBAAC,eAAe;;;;;;;;;;;;;;;;;AAI3C", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,0OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,8PAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { DropdownMenu as DropdownMenuPrimitive } from \"radix-ui\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,mTAAC,+TAAA,CAAA,eAAqB,CAAC,IAAI;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,MAAM;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,OAAO;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,MAAM;kBAC3B,cAAA,mTAAC,+TAAA,CAAA,eAAqB,CAAC,OAAO;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,KAAK;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,IAAI;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,YAAY;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,mTAAC;gBAAK,WAAU;0BACd,cAAA,mTAAC,+TAAA,CAAA,eAAqB,CAAC,aAAa;8BAClC,cAAA,mTAAC,yRAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,UAAU;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,SAAS;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,mTAAC;gBAAK,WAAU;0BACd,cAAA,mTAAC,+TAAA,CAAA,eAAqB,CAAC,aAAa;8BAClC,cAAA,mTAAC,2RAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,KAAK;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,SAAS;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,mTAAC,+TAAA,CAAA,eAAqB,CAAC,GAAG;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,UAAU;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,mTAAC,2SAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,mTAAC,+TAAA,CAAA,eAAqB,CAAC,UAAU;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/mode-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ModeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AANA;;;;;;AAaO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,8NAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,mTAAC,2JAAA,CAAA,eAAY;;0BACX,mTAAC,2JAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,mTAAC,iJAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,mTAAC,iRAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,mTAAC,mRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,mTAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,mTAAC,2JAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,mTAAC,2JAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,mTAAC,2JAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,mTAAC,2JAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/lib/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from \"better-auth/react\";\n\nexport const authClient = createAuthClient({\n  baseURL:\n      process.env.NEXT_PUBLIC_SERVER_URL,\n});\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,CAAA,GAAA,iPAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,OAAO;AAET", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,mTAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/user-menu.tsx"], "sourcesContent": ["import {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { authClient } from \"@/lib/auth-client\";\nimport { Button } from \"./ui/button\";\nimport { Skeleton } from \"./ui/skeleton\";\nimport { useRouter } from \"next/navigation\";\nimport Link from \"next/link\";\n\nexport default function UserMenu() {\n  const router = useRouter();\n  const { data: session, isPending } = authClient.useSession();\n\n  if (isPending) {\n    return <Skeleton className=\"h-9 w-24\" />;\n  }\n\n  if (!session) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <Link href=\"/login\">Sign In</Link>\n      </Button>\n    );\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\">{session.user.name}</Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"bg-card\">\n        <DropdownMenuLabel>My Account</DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        <DropdownMenuItem>{session.user.email}</DropdownMenuItem>\n        <DropdownMenuItem asChild>\n          <Button\n            variant=\"destructive\"\n            className=\"w-full\"\n            onClick={() => {\n              authClient.signOut({\n                fetchOptions: {\n                  onSuccess: () => {\n                    router.push(\"/\");\n                  },\n                },\n              });\n            }}\n          >\n            Sign Out\n          </Button>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,2IAAA,CAAA,aAAU,CAAC,UAAU;IAE1D,IAAI,WAAW;QACb,qBAAO,mTAAC,mJAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,mTAAC,iJAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,mTAAC,iOAAA,CAAA,UAAI;gBAAC,MAAK;0BAAS;;;;;;;;;;;IAG1B;IAEA,qBACE,mTAAC,2JAAA,CAAA,eAAY;;0BACX,mTAAC,2JAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,mTAAC,iJAAA,CAAA,SAAM;oBAAC,SAAQ;8BAAW,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;0BAE9C,mTAAC,2JAAA,CAAA,sBAAmB;gBAAC,WAAU;;kCAC7B,mTAAC,2JAAA,CAAA,oBAAiB;kCAAC;;;;;;kCACnB,mTAAC,2JAAA,CAAA,wBAAqB;;;;;kCACtB,mTAAC,2JAAA,CAAA,mBAAgB;kCAAE,QAAQ,IAAI,CAAC,KAAK;;;;;;kCACrC,mTAAC,2JAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,mTAAC,iJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;gCACP,2IAAA,CAAA,aAAU,CAAC,OAAO,CAAC;oCACjB,cAAc;wCACZ,WAAW;4CACT,OAAO,IAAI,CAAC;wCACd;oCACF;gCACF;4BACF;sCACD;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Projects/Work/realtor-biz-africa/apps/web/src/components/header.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\n\nimport { ModeToggle } from \"./mode-toggle\";\nimport UserMenu from \"./user-menu\";\n\nexport default function Header() {\n  const links = [\n    { to: \"/\", label: \"Home\" },\n      { to: \"/dashboard\", label: \"Dashboard\" },\n  ];\n\n  return (\n    <div>\n      <div className=\"flex flex-row items-center justify-between px-2 py-1\">\n        <nav className=\"flex gap-4 text-lg\">\n          {links.map(({ to, label }) => {\n            return (\n              <Link key={to} href={to}>\n                {label}\n              </Link>\n            );\n          })}\n        </nav>\n        <div className=\"flex items-center gap-2\">\n          <ModeToggle />\n          <UserMenu />\n        </div>\n      </div>\n      <hr />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAK,OAAO;QAAO;QACvB;YAAE,IAAI;YAAc,OAAO;QAAY;KAC1C;IAED,qBACE,mTAAC;;0BACC,mTAAC;gBAAI,WAAU;;kCACb,mTAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE;4BACvB,qBACE,mTAAC,iOAAA,CAAA,UAAI;gCAAU,MAAM;0CAClB;+BADQ;;;;;wBAIf;;;;;;kCAEF,mTAAC;wBAAI,WAAU;;0CACb,mTAAC,mJAAA,CAAA,aAAU;;;;;0CACX,mTAAC,iJAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;;0BAGb,mTAAC;;;;;;;;;;;AAGP", "debugId": null}}]}