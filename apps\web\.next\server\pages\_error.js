const CHUNK_PUBLIC_PATH = "server/pages/_error.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules__bun_e896c01d._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__c75c51b7._.js");
runtime.loadChunk("server/chunks/ssr/466aa_next_6a486baf._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__bun_8eef187d._.js");
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/node_modules/.bun/next@15.3.0+498059a1009c1789/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
