"use client"

import { useActionState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Users, Plus } from "lucide-react"
import { createTenant } from "@/lib/actions"

interface TenantFormProps {
  leases?: Array<{ id: string; propertyName: string; startDate: string; endDate: string }>
}

export function TenantForm({ leases = [] }: TenantFormProps) {
  const [state, formAction] = useActionState(createTenant, null)

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          Add Tenant
        </CardTitle>
        <CardDescription>Add tenant information for a lease agreement</CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-6">
          {/* Lease Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Lease Agreement</h3>
            <div className="space-y-2">
              <Label htmlFor="lease_id">Select Lease Agreement</Label>
              <Select name="lease_id" required>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a lease agreement" />
                </SelectTrigger>
                <SelectContent>
                  {leases.length > 0 ? (
                    leases.map((lease) => (
                      <SelectItem key={lease.id} value={lease.id}>
                        {lease.propertyName} ({lease.startDate} - {lease.endDate})
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      No lease agreements available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {state?.errors?.lease_id && <p className="text-sm text-red-500">{state.errors.lease_id[0]}</p>}
            </div>
          </div>

          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name">First Name</Label>
                <Input id="first_name" name="first_name" placeholder="e.g., John" required />
                {state?.errors?.first_name && <p className="text-sm text-red-500">{state.errors.first_name[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name">Last Name</Label>
                <Input id="last_name" name="last_name" placeholder="e.g., Doe" required />
                {state?.errors?.last_name && <p className="text-sm text-red-500">{state.errors.last_name[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="date_of_birth">Date of Birth</Label>
                <Input id="date_of_birth" name="date_of_birth" type="date" required />
                {state?.errors?.date_of_birth && (
                  <p className="text-sm text-red-500">{state.errors.date_of_birth[0]}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="national_identity_number">National Identity Number</Label>
                <Input
                  id="national_identity_number"
                  name="national_identity_number"
                  placeholder="e.g., SSN or ID number"
                  required
                />
                {state?.errors?.national_identity_number && (
                  <p className="text-sm text-red-500">{state.errors.national_identity_number[0]}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" name="email" type="email" placeholder="e.g., <EMAIL>" required />
                {state?.errors?.email && <p className="text-sm text-red-500">{state.errors.email[0]}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input id="phone" name="phone" type="tel" placeholder="e.g., +****************" required />
                {state?.errors?.phone && <p className="text-sm text-red-500">{state.errors.phone[0]}</p>}
              </div>
            </div>
          </div>

          {/* Success/Error Messages */}
          {state?.success && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">Tenant added successfully!</p>
            </div>
          )}
          {state?.error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800">{state.error}</p>
            </div>
          )}

          <Button type="submit" className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            Add Tenant
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
