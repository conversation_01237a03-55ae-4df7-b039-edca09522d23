# Dashboard Implementation

This document describes the implementation of the dashboard functionality with API calls, caching, and property count display.

## Features Implemented

### 1. React Query Setup
- Added React Query (TanStack Query) to the providers for efficient data fetching and caching
- Configured with sensible defaults:
  - `staleTime`: 5 minutes
  - `gcTime`: 10 minutes (cache time)
  - `retry`: 1 attempt
  - `refetchOnWindowFocus`: false

### 2. Custom Hook for Property Data
- Created `useProperties` hook in `/src/hooks/useProperties.ts`
- Handles API calls to `/api/property` endpoint
- Includes proper TypeScript interfaces for type safety
- Automatically includes authentication headers from the session

### 3. Property Context Provider
- Created `PropertyContext` in `/src/contexts/PropertyContext.tsx`
- Provides cached property data to child components
- Includes loading states, error handling, and refetch functionality
- Prevents unnecessary re-renders and API calls

### 4. Dashboard Updates
- Updated dashboard page to use the new caching system
- Displays real-time property count with loading states
- Shows error states with user-friendly messages
- Added refresh button to manually trigger data refetch

### 5. Property List Component
- Created reusable `PropertyList` component
- Demonstrates how cached data can be shared across components
- Includes loading skeletons and empty states
- Shows property details with badges for vacancy status

## File Structure

```
apps/web/src/
├── hooks/
│   └── useProperties.ts          # Custom hook for property data
├── contexts/
│   └── PropertyContext.tsx      # Context provider for sharing cached data
├── components/
│   ├── providers.tsx            # Updated with React Query setup
│   └── PropertyList.tsx         # Property list component
└── app/dashboard/
    └── page.tsx                 # Updated dashboard page
```

## API Integration

The implementation integrates with the existing `/api/property` endpoint:

- **Endpoint**: `GET /api/property`
- **Authentication**: Bearer token from session
- **Response Format**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": "string",
        "property_name": "string",
        "plot_number": "string",
        "street_name": "string",
        "city": "string",
        "state": "string",
        "country": "string",
        "bedrooms": number,
        "bathrooms": number,
        "base_rent": number,
        "base_deposit": number,
        "currency": "string",
        "listing_date": "string",
        "vacant": boolean
      }
    ]
  }
  ```

## Caching Strategy

- **React Query**: Provides intelligent caching with automatic background updates
- **Stale-while-revalidate**: Shows cached data immediately while fetching fresh data
- **Context Sharing**: Cached data is shared across components without additional API calls
- **Manual Refresh**: Users can manually refresh data with the refresh button

## Error Handling

- Network errors are caught and displayed to users
- Loading states prevent user confusion during API calls
- Toast notifications provide feedback for actions
- Graceful fallbacks for empty or error states

## Performance Benefits

1. **Reduced API Calls**: Data is cached and shared across components
2. **Faster UI Updates**: Cached data is displayed immediately
3. **Background Updates**: Fresh data is fetched in the background
4. **Optimistic Updates**: UI updates immediately while API calls are in progress

## Usage

The dashboard now automatically:
1. Fetches property data when the user is authenticated
2. Displays the total property count
3. Shows loading states during API calls
4. Handles errors gracefully
5. Provides cached data to child components
6. Allows manual refresh of data

The cached property data can be accessed in any child component using:
```tsx
import { usePropertyContext } from "@/contexts/PropertyContext";

function MyComponent() {
  const { properties, propertyCount, isLoading, error, refetch } = usePropertyContext();
  // Use the cached data...
}
```
